# 压缩模块性能指标统计功能设计文档

## 概述

本设计文档基于对现有上传模块统计机制的分析，为压缩模块设计统一的性能指标统计框架。通过分析现有代码发现，上传模块使用滑动窗口机制进行实时统计，我们将基于此设计兼容的统计框架，并扩展支持压缩相关的性能指标。

## 架构

### 现有上传统计机制分析

通过代码分析发现，现有上传模块的统计机制具有以下特点：

1. **滑动窗口统计**：使用环形缓冲区实现10秒滑动窗口
2. **时间片统计**：每1秒为一个时间片进行数据聚合
3. **线程独立统计**：每个上传线程维护独立的统计数据
4. **实时更新**：在 `__ft_upload_data` 函数中调用 `ft_up_stat_update` 更新统计

### 统一统计框架架构

```
┌─────────────────────────────────────────────────────────────┐
│                    统一统计框架                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   通用统计核心   │    │   指标类型管理   │                │
│  │                │    │                │                │
│  │ - 滑动窗口实现   │    │ - 上传指标      │                │
│  │ - 时间片管理     │    │ - 压缩指标      │                │
│  │ - 环形缓冲区     │    │ - 扩展指标      │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   上传统计模块   │    │   压缩统计模块   │                │
│  │                │    │                │                │
│  │ - 传输速率      │    │ - 压缩速率      │                │
│  │ - 文件数量      │    │ - 压缩比率      │                │
│  │                │    │ - 处理文件数     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 组件和接口

### 1. 通用统计数据结构

```c
/* 统计指标类型 */
enum ft_stat_metric_type {
    FT_STAT_METRIC_UPLOAD = 0,      /* 上传指标 */
    FT_STAT_METRIC_COMPRESS,        /* 压缩指标 */
    FT_STAT_METRIC_MAX
};

/* 通用时间片统计数据 */
struct ft_stat_slice {
    volatile time_t timestamp;      /* 时间片时间戳 */
    volatile size_t bytes;          /* 该时间片内处理字节数 */
    volatile uint64_t files;        /* 该时间片内处理文件数 */
    volatile double ratio;          /* 该时间片内平均比率（压缩比等） */
};

/* 通用统计结构 */
struct ft_performance_stat {
    struct ft_stat_slice window[FT_STAT_WINDOW_SIZE];  /* 环形缓冲区数据 */
    volatile uint8_t current_idx;   /* 当前写入索引 */
    volatile time_t last_update;    /* 最后更新时间 */
    enum ft_stat_metric_type type;  /* 统计指标类型 */
};
```

### 2. 压缩统计扩展结构

```c
/* 压缩线程统计数据 */
struct ft_compress_stat {
    struct ft_performance_stat perf_stat;  /* 通用性能统计 */
    volatile size_t total_input_bytes;     /* 累计输入字节数 */
    volatile size_t total_output_bytes;    /* 累计输出字节数 */
    volatile uint64_t total_files;         /* 累计处理文件数 */
};
```

### 3. 统一API接口

```c
/* 通用统计接口 */
int ft_stat_init(enum ft_stat_metric_type type, uint8_t thread_idx);
int ft_stat_update(enum ft_stat_metric_type type, uint8_t thread_idx, 
                   size_t bytes, uint64_t files, double ratio);
int ft_stat_get(enum ft_stat_metric_type type, uint8_t thread_idx,
                double *rate, double *fps, double *avg_ratio);

/* 压缩专用统计接口 */
int ft_comp_stat_init(uint8_t comp_idx);
int ft_comp_stat_update(uint8_t comp_idx, size_t input_bytes, 
                        size_t output_bytes, uint64_t files);
int ft_comp_stat_get(uint8_t comp_idx, double *compress_rate_KBps, 
                     double *fps, double *compression_ratio);

/* 兼容现有上传接口 */
void ft_up_stat_init(uint8_t up_idx);
void ft_up_stat_update(uint8_t up_idx, size_t bytes, uint64_t files);
void ft_up_stat_get(double *KBps, double *fps, uint8_t up_idx);
```

## 数据模型

### 1. 统计数据存储

```c
/* 在 ft_comp_thread 结构中添加统计字段 */
struct ft_comp_thread {
    // ... 现有字段 ...
    struct ft_compress_stat stat;   /* 压缩统计数据 */
};

/* 全局统计管理 */
struct ft_stat_manager {
    struct ft_performance_stat upload_stats[FT_THREAD_MAX];
    struct ft_compress_stat compress_stats[FT_THREAD_MAX];
    uint8_t upload_count;
    uint8_t compress_count;
};
```

### 2. 压缩指标计算

- **压缩速率**：基于输入数据大小和处理时间计算
- **压缩比率**：输入大小 / 输出大小
- **处理文件数**：单位时间内处理的文件数量

## 错误处理

### 1. 统计更新错误处理

- 线程索引越界检查
- 时间戳异常处理
- 内存访问保护

### 2. 数据一致性保证

- 使用 volatile 关键字保证内存可见性
- 原子操作更新关键统计数据
- 避免统计更新影响主要功能性能

## 测试策略

### 1. 单元测试

- 统计数据结构初始化测试
- 滑动窗口更新逻辑测试
- 压缩比率计算准确性测试

### 2. 集成测试

- 压缩模块统计集成测试
- 多线程并发统计测试
- 长时间运行稳定性测试

### 3. 性能测试

- 统计更新对压缩性能影响测试
- 内存使用量测试
- 统计查询响应时间测试

## 实现细节

### 1. 文件组织结构

**统一到filetrans模块**
```
src/filetrans/
├── ft_stat.h           # 统计框架头文件
├── ft_stat.c           # 统计框架实现
├── ft_compress.c       # 压缩模块（已存在，需要修改）
├── ft_upload.c         # 上传模块（需要重构）
└── include/
    └── filetrans.h     # 对外API接口（添加统计接口声明）
```

### 2. 关键实现点

1. **统计更新位置**：
   - 压缩模块：在 `compress_dispose_file` 函数完成后更新
   - 上传模块：保持现有 `__ft_upload_data` 中的更新位置

2. **线程安全**：
   - 每个线程维护独立的统计数据，避免锁竞争
   - 使用原子操作更新共享计数器

3. **内存管理**：
   - 统计数据作为线程结构的一部分，随线程生命周期管理
   - 避免动态内存分配，提高性能

### 3. 兼容性考虑

- 保持现有上传统计API不变
- 新增的压缩统计API采用类似命名规范
- 统一的内部实现，减少代码重复

## 扩展性设计

### 1. 新指标类型支持

框架设计支持未来添加新的统计指标类型：
- 网络传输指标
- 存储I/O指标
- 系统资源使用指标

### 2. 配置化支持

- 滑动窗口大小可配置
- 时间片粒度可调整
- 统计开关控制

### 3. 监控集成

- 提供标准化的统计数据导出接口
- 支持与外部监控系统集成
- 日志记录关键统计事件