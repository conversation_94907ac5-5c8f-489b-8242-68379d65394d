# 压缩模块性能指标统计功能需求文档

## 介绍

本功能旨在为 filetrans 项目的压缩模块设计并实现类似于现有上传模块的性能指标统计功能。通过分析现有上传模块的统计机制，设计统一的统计框架，为压缩模块提供压缩速率、压缩比等关键性能指标的实时统计和查询能力。

## 需求

### 需求 1：分析现有上传统计机制

**用户故事：** 作为开发者，我希望了解现有上传模块的统计实现机制，以便为压缩模块设计兼容的统计功能。

#### 验收标准

1. WHEN 分析现有代码 THEN 系统 SHALL 识别出上传模块使用滑动窗口统计机制
2. WHEN 查看统计数据结构 THEN 系统 SHALL 发现 ft_upload_stat 结构包含环形缓冲区和时间片统计
3. WHEN 检查统计接口 THEN 系统 SHALL 找到 ft_up_stat_init、ft_up_stat_update、ft_up_stat_get 三个核心函数
4. WHEN 分析统计调用位置 THEN 系统 SHALL 确认统计更新在 __ft_upload_data 函数中调用

### 需求 2：设计压缩模块统计指标

**用户故事：** 作为系统管理员，我希望能够监控压缩模块的性能指标，包括压缩速率和压缩比等关键数据。

#### 验收标准

1. WHEN 定义压缩统计指标 THEN 系统 SHALL 包含压缩速率（KB/s）统计
2. WHEN 定义压缩统计指标 THEN 系统 SHALL 包含压缩比率统计
3. WHEN 定义压缩统计指标 THEN 系统 SHALL 包含处理文件数量统计
4. WHEN 设计数据结构 THEN 系统 SHALL 与现有上传统计结构保持兼容
5. WHEN 使用滑动窗口 THEN 系统 SHALL 采用类似上传模块的时间片统计方式

### 需求 3：实现统一的统计框架

**用户故事：** 作为开发者，我希望有一个统一的统计框架，能够被上传和压缩模块共同使用，避免代码重复。

#### 验收标准

1. WHEN 创建统计框架 THEN 系统 SHALL 提供通用的滑动窗口统计实现
2. WHEN 设计统计接口 THEN 系统 SHALL 支持不同类型的性能指标统计
3. WHEN 设计API接口 THEN 系统 SHALL 提供初始化、更新、查询三类基础操作
4. WHEN 考虑扩展性 THEN 系统 SHALL 支持未来添加新的统计指标类型
5. WHEN 实现每线程统计 THEN 系统 SHALL 为每个线程维护独立的统计数据

### 需求 4：集成到压缩模块

**用户故事：** 作为系统用户，我希望压缩模块能够自动收集和更新性能统计数据，无需额外配置。

#### 验收标准

1. WHEN 压缩处理完成 THEN 系统 SHALL 获取输入数据大小和输出数据大小
2. WHEN 压缩完成 THEN 系统 SHALL 自动更新统计数据到滑动窗口
3. WHEN 计算压缩比 THEN 系统 SHALL 使用输入大小除以输出大小的公式
4. WHEN 更新统计 THEN 系统 SHALL 不影响现有压缩功能的性能
5. WHEN 集成统计调用 THEN 系统 SHALL 在压缩处理函数的适当位置添加统计更新

### 需求 5：更新现有上传统计

**用户故事：** 作为开发者，我希望现有的上传统计代码能够使用新的统一框架，保持代码一致性。

#### 验收标准

1. WHEN 重构上传统计 THEN 系统 SHALL 使用新的统一框架实现
2. WHEN 更新API接口 THEN 系统 SHALL 保持接口命名和调用方式的一致性
3. WHEN 迁移代码 THEN 系统 SHALL 不影响现有上传功能的稳定性
4. WHEN 测试兼容性 THEN 系统 SHALL 确保统计数据的准确性不变

### 需求 6：提供查询接口

**用户故事：** 作为监控系统，我希望能够查询压缩模块的实时性能数据，用于系统监控和性能分析。

#### 验收标准

1. WHEN 查询压缩速率 THEN 系统 SHALL 返回最近时间窗口内的平均压缩速率
2. WHEN 查询压缩比 THEN 系统 SHALL 返回最近时间窗口内的平均压缩比
3. WHEN 查询处理量 THEN 系统 SHALL 返回最近时间窗口内的文件处理数量
4. WHEN 提供统计接口 THEN 系统 SHALL 支持按线程索引查询特定压缩线程的统计数据
5. WHEN 单线程查询 THEN 系统 SHALL 提供简单直接的查询接口实现