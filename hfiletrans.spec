%define debug_package %{nil}

%{lua:
    local handle = io.popen("cat /etc/system-release | awk -F ' ' '{print $1}'")
    local SysType = handle:read("*a")
    handle:close()

    local SysVer

    if SysType:match("^openEuler") then
        handle = io.popen("cat /etc/system-release | grep -o 'release.*' | awk -F ' ' '{print $2}' | awk -F '.' '{print $1$2}'")
        SysVer = handle:read("*a"):gsub("%s*$", "")

        handle = io.popen("cat /etc/system-release | grep SP")
        local rst = handle:read("*a")
        handle:close()

        if string.len(rst) > 0 then
            if rst:match("SP1") then
                SysVer = "oe" .. SysVer .. "sp1"
            elseif rst:match("SP2") then
                SysVer = "oe" .. SysVer .. "sp2"
            end
        else
            SysVer = "oe" .. SysVer
        end

    elseif SysType:match("^BigCloud") then
        SysType = "bclinux"
        handle = io.popen("cat /etc/system-release | awk -F 'release' '{print $2}' | awk -F ' ' '{print $1}' | sed 's/[.]//g'")
        SysVer = handle:read("*a"):gsub("%s*$", "")
        handle:close()

        handle = io.popen("cat /etc/system-release | grep -o 'U[1-2]'")
        local rst = handle:read("*a")
        handle:close()

        if string.len(rst) > 0 then
            if rst:match("U1") then
                SysVer = "bc" .. SysVer .. "u1"
            elseif rst:match("U2") then
                SysVer = "bc" .. SysVer .. "u2"
            end
        else
            SysVer = "bc" .. SysVer
        end

    elseif SysType:match("^CentOS") then
        handle = io.popen("cat /etc/system-release | grep -o 'release.*' | awk -F ' ' '{print $2}' | awk -F '.' '{print $1}'")
        SysVer = handle:read("*a"):gsub("%s*$", "")
        SysVer = "el" .. SysVer
        handle:close()
    else
        handle = io.popen("cat /etc/system-release | grep -o 'release.*' | awk -F ' ' '{print $2}' | awk -F '.' '{print $1}'")
        SysVer = handle:read("*a"):gsub("%s*$", "")
        handle:close()
    end

    handle = io.popen("uname -i")
    local CoreAr = handle:read("*a")
    local CoreArAbbr
    handle:close()

    if CoreAr:match("^x86_64") then
        CoreArAbbr = "x64"
    elseif CoreAr:match("^x86_32") then
        CoreArAbbr = "x32"
    elseif CoreAr:match("^aarch64") then
        CoreArAbbr = "aarch64"
    end

    rpm.define("SysType " .. SysType)
    rpm.define("SysVer " .. SysVer)
    rpm.define("CoreAr " .. CoreAr)
    rpm.define("CoreArAbbr " .. CoreArAbbr)
}

Name:           hh-filetrans
Version:        0.1.2
Release:        1.%{SysVer}
Source0:        hhfiletrans-0.1.2-release-centos-x86_64.tar.gz
License:        GPLv2
Summary:        haohan filetrans library

%description
haohan filetrans library

%prep
%setup -q -n hhfiletrans-0.1.2-release-centos-x86_64

%build

%install
mkdir -p $RPM_BUILD_ROOT/usr/local/lib64
cp -a -r $(pwd)/lib64/* $RPM_BUILD_ROOT/usr/local/lib64/

mkdir -p $RPM_BUILD_ROOT/usr/local/include/
cp -a $(pwd)/include/* $RPM_BUILD_ROOT/usr/local/include/

%clean
# clean temp file after compliered
[ "$RPM_BUILD_ROOT" != "/" ] && rm -rf $RPM_BUILD_ROOT
rm -rf %{_builddir}/

%files
/usr/local/lib64/*
/usr/local/include/*

%changelog
* Mon Oct 14 2024 zhengpeng <<EMAIL>> - %{version} -1
- Initial packaging for RPM，for HDS
