#!/bin/bash

#
# 统一输出用户安装脚本
#
# 注意:
# 1. 目前仅支持以下操作系统
#    - Ubuntu 16+
#    - CentOS 7+
#    其他系统可能需要手动修改部分脚本, 或自行下载软件包安装
# 2. 系统需要支持基本软件源, 因为python3等基本软件包需要从软件源安装
#


workdir=$(pwd)
pkgdir=
prefix=/usr/local
install_depends=true
ostype=

function usage {
    echo "usage: $0
    [-h] show help
    [--pkgdir DIR] hhfiletrans package directory
    [--prefix PREFIX] hhfiletrans install directory, it can be empty. default: $prefix
    "
}

# https://stackoverflow.com/questions/402377/using-getopts-to-process-long-and-short-command-line-options
# https://www.shellscript.sh/tips/getopt/
ARGS=$(getopt -o h --long pkgdir:,prefix:,install-depends:,depends-dir:,depends-url: \
    -n "install.sh" -- "$@")
VALID_ARGS=$?
if [ "$VALID_ARGS" != "0" ]; then
    usage
    exit 1
fi

#echo "ARGS is $ARGS"
eval set -- "$ARGS"

while true; do
    case "$1" in
    -h)
        usage
        exit 0
        shift
        ;;
    --pkgdir)
        pkgdir="$2"
        shift 2
        ;;
    --prefix)
        prefix="$2"
        shift 2
        ;;
    # -- means the end of the arguments; drop this, and break out of the while loop
    --)
        shift
        break
        ;;
    # If invalid options were passed, then getopt should have reported an error,
    # which we checked as VALID_ARGUMENTS when getopt was called...
    *)
        echo "unexpected option: %1"
        usage
        exit 1
        ;;
    esac
done

function pre_check {
    local pcfile

    if [ -z "$pkgdir" ] || [ ! -d "$pkgdir" ]; then
        echo "error: invalid pkgdir"
        usage
        exit 1
    fi

    osstr=$(cat /etc/system-release | awk '{print $1}')
    case $osstr in
    CentOS)
        ostype='centos'
        ;;
    BigCloud)
        ostype='bclinux'
        ;;
    openEuler)
        ostype='openEuler'
        ;;
    uos)
        ostype='uos'
        ;;
    Anolis)
        ostype='Anolis'
        ;;
    *)
        echo "error: OS unsupported"
        exit 1
        ;;
    esac
}

function install_hhfiletrans {

    echo "install hhfiletrans..."

    if [ ! -d "${prefix}" ]; then
        echo "error: $prefix is not a directory"
        exit 1
    fi

    echo -n ${prefix} >./PREFIX
    echo "install to: ${prefix}"

    if [ $ostype == "ubuntu" ]; then
        cp -ar $pkgdir/include $prefix/
        mkdir -p $prefix/lib/x86_64-linux-gnu
        cp -ar $pkgdir/lib64/* $prefix/lib/x86_64-linux-gnu/
    else
        cp -ar $pkgdir/include $prefix/
        cp -ar $pkgdir/lib64 $prefix/
    fi
}

function uninstall_hhfiletrans {
    # todo:
    echo "todo..."
}

###################################################################################################

pre_check

if [ ! -z "$prefix" ]; then
    install_hhfiletrans
fi

echo "done"
