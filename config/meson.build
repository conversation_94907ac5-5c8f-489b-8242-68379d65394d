machine_args = []
ext_args = []

supported_oss = ['linux']
if not supported_oss.contains(host_os_name)
    error('unsupported system type @0@'.format(host_os_name))
endif

foreach os : supported_oss
    set_variable('is_' + os, host_os_name == os)
    ft_conf.set('FT_OS_' + host_os_name.to_upper(), 1)
endforeach

# set the major version, which might be used by drivers and libraries
# depending on the configuration options
ft_ver = meson.project_version().split('.')
ft_major_ver = '@0@.@1@'.format(ft_ver.get(0), ft_ver.get(1))
# Libraries have the abi_version as the filename extension
# and have the soname be all but the final part of the abi_version.
# e.g. v1.1 => libft.so.1.1
#    sonames => libft.so.1
#so_version = abi_version.split('.')[0]
ft_so_version = ft_ver.get(0)

# extract all version information into the build configuration
ft_conf.set('FT_VER_MAJOR', ft_ver.get(0).to_int())
ft_conf.set('FT_VER_MINOR', ft_ver.get(1).to_int())
ft_conf.set('FT_VER_PATCH', ft_ver.get(2).to_int())
ft_conf.set_quoted('FT_VER_STRING', meson.project_version())
branch = run_command(find_program('git'), 'branch', '--show-current').stdout().strip()
commit = run_command(find_program('git'), 'log', '-1', '--pretty=format:%h').stdout().strip()
ft_conf.set('FT_VER_BRANCH', branch)
ft_conf.set_quoted('FT_VER_COMMIT', commit)
ft_conf.set(
    'FT_VER_STRING_FULL',
    '"@0@ @1@ @2@"'.format(meson.project_version(), branch, commit),
)

# set the machine type and cflags for it
if meson.is_cross_build()
    machine = host_machine.cpu()
else
    machine = get_option('machine')
endif

ft_conf.set_quoted('FT_CPU', host_machine.cpu())

# machine type 'default' is special, it defaults to the per arch agreed common
# minimal baseline needed for DPDK.
# That might not be the most optimized, but the most portable version while
# still being able to support the CPU features required for DPDK.
# This can be bumped up by the DPDK project, but it can never be an
# invariant like 'native'
if machine == 'default'
    if host_cpu_name.startswith('x86')
        # matches the old pre-meson build systems default
        machine = 'corei7'
    elif host_cpu_name.startswith('arm')
        machine = 'armv7-a'
    elif host_cpu_name.startswith('aarch')
        # arm64 manages defaults in config/arm/meson.build
        machine = 'default'
    elif host_cpu_name.startswith('ppc')
        machine = 'power8'
    endif
endif

machine_args += '-march=' + machine

toolchain = cc.get_id()
# https://stackoverflow.com/questions/63665653/different-behavior-between-clang-and-gcc-10-when-linking-to-static-library-conta
if (toolchain == 'clang')
    ext_args += '-fno-common'
endif

ft_conf.set('FT_MACHINE', machine)

ft_conf.set('FT_ARCH_64', cc.sizeof('void *') == 8)
ft_conf.set('FT_ARCH_32', cc.sizeof('void *') == 4)

# for linux link against dl, for bsd execinfo
if is_linux
    link_lib = ''
    # elif is_freebsd
    #     link_lib = 'execinfo'
else
    link_lib = ''
endif

# if link_lib is empty, do not add it to project properties
if link_lib != ''
    add_project_link_arguments('-l' + link_lib, language: 'c')
    ft_extra_ldflags += '-l' + link_lib
endif

# specify -D_GNU_SOURCE unconditionally
add_project_arguments('-D_GNU_SOURCE', language: 'c')
# add -include ft_config to cflags
add_project_arguments('-include', 'ft_build_config.h', language: 'c')
