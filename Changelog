0.1.4 2025-07-18
- commit:e5567b4f802211c9e7da5219e723d10aa8144d32
    - 修复tsf相关统计竞争问题
    - 去除文件处理的mmap使用，基于文件流处理
0.1.3 2025-07-14
- commit:ca62185c68ff0f3afe25c66bea3ae8a807ecf68f
    - 支持按目录进行上报，即上报目录内的所有文件(不含子目录) redmine#2294#note-41
    - 支持zip格式只打包不压缩
    - 支持业务tsf的子类型字符串定义，用于备份目录等
    - 支持上报速率统计，包括Bps，fps等指标
    - 上报接口提供用户自定义参数，可自定义文件夹内文件上传顺序
0.1.2 2025-06-20
- commit:4d1eea19f9f6b86a9c24cc0f3c40d4360902f608
    - 支持file_info用户属性自定义
    - 支持多进程交互模式(类output进程)
    - 调整部分日志级别，优化并封装检查逻辑
    - 支持计数清空
    - 优化异常落盘处理及信号处理，并处理异常落盘相关问题
    - 减小tsf配置更新的锁粒度
    - 支持本地压缩路径动态生成
    - 变更服务器配置判断到acq线程处理流程，并处理落入异常缓存
    - 队列内存池由hlib替换为dpdk，添加相关计数
    - 添加安装脚本，支持基于版本包的一键安装
    - 修复abnormal_info文件处理单行缓存不够导致的后续处理异常问题
    - 修复异常缓存文件指针泄露问题
    - 替换strtok，避免可能出现的解析问题
    - 修复zip列表压缩出现得note回收挂死问题
    - 修复ssl验证导致的上报失败问题
    - 修改上报线程队列大小65536
    - 修改运维逻辑为非备份即删除
    - 修复开启多线程的队列创建问题
    - 修复tar.gz格式的文件列表压缩格式问题
    - 修复运维锁时序错误导致的无法上报问题
    - 新增运维空操作
0.1.1 2025-03-20
    - 补充zip包压缩加密功能,对应tsf1例
    - 修复文件夹/文件列表类文件压缩偶现挂死问题
    - 支持acq/upload线程私有资源创建
    - 支持业务tsf配置动态更新能力
    - 添加统一删除/统一备份能力
    - 修复异常上报记录错误问题
    - 压缩文件类型，默认同步到磁盘
    - 交由统一上报支持配置定义相关文件属性
    - acq线程添加公共线程资源，供特殊场景使用
    - 添加platform上报平台设计，提供一些info的新接口
    - 完善压缩文件名长度宏定义
    - 处理区分平台/子类型的文件备份流程
    - 支持sftp改名时的家目录替换
    - 支持rpm打包
    - x86环境下machine指定core-avx-i，避免运行环境的指令兼容问题
    - 解决rpm包构建时的rpath检查问题
    - 数据处理过程中添加tsf注册检查，以避免可能挂死的问题
    - 修改sftp改名处理，不再处理家目录，剥去'~'
0.1.0 2025-01-03
    - 首次添加filetrans工程
    - 独立压缩库，统一上报适配独立压缩库
    - 添加example例tsf1/tsf2/tsf3/tsf4, 包含sftp/ftp，dipatcher/copy，none/tgz/zip/gz组合例
    - 添加output类线程，模拟file/list/dir等压缩样例
