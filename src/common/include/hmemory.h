#ifndef __HMEMORY_H__
#define __HMEMORY_H__

#include <stddef.h>
#include <stdlib.h>

#ifdef __cplusplus
extern "C"
{
#endif

/* 返回码定义 */
#define HMEM_OK 0
#define HMEM_FAIL -1

/* 内存分配器函数指针类型 */
typedef void *(*hmem_malloc_f)(size_t size);
typedef void (*hmem_free_f)(void *ptr);

/* 默认内存分配器 */
static inline void *hmem_default_malloc(size_t size)
{
    return malloc(size);
}

static inline void hmem_default_free(void *ptr)
{
    free(ptr);
}

static hmem_malloc_f g_hmem_malloc = hmem_default_malloc;
static hmem_free_f g_hmem_free = hmem_default_free;

/**
 * 设置统一内存分配器，供filetrans和compress两个库共同使用
 *
 * @param alloc
 *  内存分配函数指针，不能为NULL
 * @param free
 *  内存释放函数指针，不能为NULL
 * @return
 *  HMEM_OK: 设置成功
 *  HMEM_FAIL: 设置失败（参数无效）
 */
static inline int hmem_allocator_set(hmem_malloc_f alloc, hmem_free_f free)
{
    if (!alloc || !free)
    {
        return HMEM_FAIL;
    }

    g_hmem_malloc = alloc;
    g_hmem_free = free;

    return HMEM_OK;
}

/**
 * 重置为默认内存分配器（标准malloc/free）
 *
 * @return
 *  HMEM_OK: 重置成功
 */
static inline int hmem_allocator_reset(void)
{
    g_hmem_malloc = hmem_default_malloc;
    g_hmem_free = hmem_default_free;

    return HMEM_OK;
}

/**
 * 获取当前内存分配函数
 *
 * @return
 *  当前内存分配函数指针
 */
static inline hmem_malloc_f hmem_get_malloc(void)
{
    return g_hmem_malloc;
}

/**
 * 获取当前内存释放函数
 *
 * @return
 *  当前内存释放函数指针
 */
static inline hmem_free_f hmem_get_free(void)
{
    return g_hmem_free;
}

/**
 * 统一的内存分配接口
 *
 * @param size
 *  要分配的内存大小
 * @return
 *  分配的内存指针，失败返回NULL
 */
static inline void *hmem_malloc(size_t size)
{
    return g_hmem_malloc(size);
}

/**
 * 统一的内存释放接口
 *
 * @param ptr
 *  要释放的内存指针
 */
static inline void hmem_free(void *ptr)
{
    if (!ptr)
    {
        return;
    }

    g_hmem_free(ptr);
}

#ifdef __cplusplus
}
#endif

#endif /* __HMEMORY_H__ */
