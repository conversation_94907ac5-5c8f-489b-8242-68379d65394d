#ifndef __FILETRANS_H__
#define __FILETRANS_H__

#include <stdint.h>
#include <sys/stat.h>
#include <curl/curl.h>
#include "compress.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

//#define DEBUG

/** md5长度 */
#define FT_MD5_LEN              16
#define FT_MD5_STR_LEN          (FT_MD5_LEN * 2)

/** filetrans服务状态 */
#define FT_FILETRANS_STOP       0
#define FT_FILETRANS_START      1

/** 支持的业务transfer最大类型值 */
#define FT_TRANSFER_TYPE_MAX           256

/** 长度 */
#define FT_MAX_LEN_32       32
#define FT_MAX_LEN_64       64
#define FT_MAX_LEN_128      128
#define FT_MAX_LEN_256      256
#define FT_MAX_LEN_512      512
#define FT_MAX_LEN_1024     1024
#define FT_MAX_LEN_8192     8192

/** 返回值 */
#define FT_OK              0
#define FT_FAIL            -1

#define __ft_unused      __attribute__((__unused__))

/** 压缩类型 */
#define FT_COMPRESS_ZIP     COMPRESS_MODE_ZIP        /* zip */
#define FT_COMPRESS_GZIP    COMPRESS_MODE_GZIP       /* gzip */
#define FT_COMPRESS_TGZ     COMPRESS_MODE_TGZ        /* tar.gz */
#define FT_COMPRESS_NONE    COMPRESS_MODE_NONE       /* 不压缩 */

/** 输入文件类型 */
enum ft_info_type {
    FT_INFO_DIR = 0,
    FT_INFO_FILE,
    FT_INFO_LIST,
};

/** 线程类型，用于查看线程资源用 */
enum ft_thread_type {
    FT_THREAD_TYPE_ACQ = 0, /* acquire */
    FT_THREAD_TYPE_COMP,    /* compress */
    FT_THREAD_TYPE_UP,      /* upload */
    FT_THREAD_TYPE_MAX
};

/* 滑动窗口统计配置 */
#define FT_STAT_WINDOW_SIZE     10      /**< 滑动窗口大小（秒数） */
#define FT_STAT_TIME_SLICE      1       /**< 时间片大小（秒） */

/* 统计指标类型 */
enum ft_stat_metric_type {
    FT_STAT_METRIC_UPLOAD = 0,      /**< 上传指标 */
    FT_STAT_METRIC_COMPRESS,        /**< 压缩指标 */
    FT_STAT_METRIC_MAX
};


/** 上报服务器模式 */
enum ft_up_srv_mode {
    FT_UP_SRV_DISP = 0,             /* 分发上报服务器 */
    FT_UP_SRV_COPY,                 /* 拷贝上报服务器 */
    //FT_UP_SRV_USER_DEFINED,         /* 用户自定义上报服务器 */
};

/** 上报模式 */
enum ft_upload_mode {
    FT_UPLOAD_SFTP = 0,
    FT_UPLOAD_FTP,
    FT_UPLOAD_MAX
};

/** ip版本 */
enum ft_ip_ver {
    FT_IP_VER4 = 0,
    FT_IP_VER6
};

/** 文件传输钩子点 */
enum ft_file_hooks {
    FT_FILE_ACQ_PRE = 0,
    FT_FILE_ACQ_BACK,
    FT_FILE_COMP_PRE,
    FT_FILE_COMP_BACK,
    FT_FILE_UPLOAD,
    FT_FILE_HOOK_MAX
};

/* 压缩级别 fastest, medium/default, slow, slowest, none */
enum ft_compress_level {
    FT_COMPRESS_LEVEL_FASTEST = 0,
    FT_COMPRESS_LEVEL_MEDIUM,
    FT_COMPRESS_LEVEL_SLOW,
    FT_COMPRESS_LEVEL_SLOWEST,
    FT_COMPRESS_LEVEL_NONE,
#define FT_COMPRESS_LEVEL_DEFAULT   FT_COMPRESS_LEVEL_MEDIUM
};

/* 日志级别 */
enum ft_log_level {
    FT_LOG_LEVEL_ALERT = 1,             /**< 严重报警 */
    FT_LOG_LEVEL_CRIT,                  /**< 关键 */
    FT_LOG_LEVEL_ERR,                   /**< 错误 */
    FT_LOG_LEVEL_WARNING,               /**< 警告 */
    FT_LOG_LEVEL_NOTICE,                /**< 普通但重要 */
    FT_LOG_LEVEL_INFO,                  /**< 有用信息 */
    FT_LOG_LEVEL_DEBUG,                 /**< 调试 */
    FT_LOG_LEVEL_MAX
};

/* 运维操作类型 */
enum ft_ops_type {
    FT_OPS_TYPE_NONE = 0,               /**< 空操作 */
    FT_OPS_TYPE_DEL,                    /**< 删除操作 */
    FT_OPS_TYPE_BACKUP,                 /**< 备份操作 */
};

/**
 * 多进程通信消息, 目前仅支持FT_INFO_FILE.
 */
struct ft_mproc_msg {
    uint8_t produced;           /**< 消息已生产 */
    uint8_t consumed;           /**< 消息已消费 */
    size_t usr_priv_sz;         /**< 可用的私有数据大小 */
    uint32_t log_type;          /**< 所属日志类型 */
    uint32_t line;              /**< 文件总行数 */
    uint16_t sub_type;          /**< 日志子类型 */
    uint16_t platform;          /**< 上报平台类型 */
    char path[FT_MAX_LEN_256];  /**< 文件或目录全路径 (/root/1234.txt) */
    char usr_priv[0];           /**< 用户私有数据空间 */
};

/**
 * 运维回调参数.
 */
struct ft_ops_cb_arg {
    uint16_t tsf_type;              /**< tsf类型 */
    uint64_t ops_sec;               /**< 操作时间(sec) */
    int plat;                       /**< 平台类型，-1为非法类型 */
    int sub_type;                   /**< 日志子类型, -1为非法类型 */
    void *acq;                      /**< acq线程上下文 */
};

/**
 * 交由acquire线程时携带参数，可选
 */
struct ft_info_attr {
    uint32_t line;              /**< 交由acquire的文件/列表/文件夹日志总行数 */
    uint16_t sub_type;          /**< 日志子类型 */
    uint16_t platform;          /**< 上报平台类型 */
    void *usr_priv;             /**< 用户自定义携带参数:内存来源于filetrans */
};

/**
 * 统一上报运维参数.
 */
struct ft_ops_cfg {
    uint8_t nor_valid;                  /**< 正常上报的文件运维: none-空操作，del-上报成功即删除，backup-上报成功即备份 */
    uint8_t abn_valid;                  /**< 异常上报的文件运维: none-空操作，del-异常超时即删除，bakckup-异常超时即备份 */
    uint8_t is_back_ori;                /**< backup: 对于上报压缩文件类，是否备份压缩前文件 */
    uint32_t del_back_day;              /**< backup: 对已备份del_back_day天的文件进行删除 */
    uint64_t abn_back_sec;              /**< backup/del: 异常文件持续异常,超时时间(单位:sec) */
    char day_time[FT_MAX_LEN_32];       /**< backup: 每日删除备份达del_back_day天的文件时刻(如00:01) */
    char nor_back_path[FT_MAX_LEN_256]; /**< backup: 正常上报文件备份主目录 */
    char abn_back_path[FT_MAX_LEN_256]; /**< backup: 异常上报文件备份主目录 */
};

/** filetrans自身处理流程日志记录接口,filetrans仅负责原始内容记录，不负责内容维护 */
typedef void (*ft_log_f)(uint8_t level, const char *format, ...);
/** filetrans异常及正常处理文件运维回调接口,目前仅会在删除时告知 */
typedef void (*ft_ops_f)(const struct ft_ops_cb_arg *arg,
                         char *path,
                         enum ft_ops_type type);

/** filetrans各类线程最大支持数 */
#define FT_THREAD_MAX       32
/**
 * 开启filetrans服务使用的参数.
 */
struct ft_cfg {
    uint8_t comp_level;                 /**< 压缩级别:默认medium/default  */
    uint8_t comp_cnt;                   /**< 文件压缩线程个数:对应绑定核心集 */
    uint8_t up_cnt;                     /**< 文件上报线程个数:对应绑定核心集 */
    uint8_t mproc;                      /**< 是否开启多进程能力 */
    uint16_t acquire_core;              /**< 文件获取线程acquire核心(目前仅支持一个, 0表示不绑定) */
    uint16_t comp_core[FT_THREAD_MAX];  /**< 压缩线程绑定核心集,无transfer有压缩业务时，不会启用 */
    uint16_t upload_core[FT_THREAD_MAX];
                                        /**< 上报线程绑定核心集 */
    char path[FT_MAX_LEN_256];          /**< filetrans自用目录(异常信息等) */
    uint8_t log_level[FT_LOG_LEVEL_MAX];
                                        /**< 处理记录日志级别，需注册 */

    struct ft_ops_cfg ops;              /**< 正常处理文件运维参数 */
    ft_log_f log_fn;                    /**< 流程处理记录日志输出接口 */
    ft_ops_f ops_fn;                    /**< tsf日志运维处理接口 */
};

/**
 * 文件自身核心信息.
 */
struct ft_file_centre {
#define FT_FILE_NAME_LEN(fc)          ((fc)->file_len + (fc)->suffix_len)
#define FT_FILE_PATH_ALL_LEN(fc)      ((fc)->file_len + (fc)->suffix_len + (fc)->path_len)
#define FT_FILE_PATH_FILE_LEN(fc)     ((fc)->file_len + (fc)->path_len)
#define FT_FILE_NAME(fc)              ((fc)->path + (fc)->path_len)
    /* eg. /root/1234.txt */
    uint16_t suffix_len;        /**< 后缀长度(含.) (.txt) */
    uint16_t file_len;          /**< 文件名长度(不含文件后缀) (1234) */
    uint16_t path_len;          /**< 文件绝对路径长度(不含文件名) (/root/) */
    char path[FT_MAX_LEN_256];  /**< 文件或目录全路径 (/root/1234.txt) */
    struct stat filestat;
    char *buf;                  /**< 映射内存部分 */
};

/**
 * filetrans待处理文件主结构.
 */
struct ft_file_info {
    uint8_t is_comp;                /**< 是否需要压缩 */
    uint16_t type;                  /**< 所属tsf类型 */
    struct ft_info_attr attr;       /**< 待处理文件属性:交由acq时携带 */
    void *res;                     /**< file_info所属资源 */
    union {
        /* 压缩文件 */
        struct {
            void *icc_res;         /**< 文件链节点所属资源 */
            struct comp_input_centre_hdr icc_hdr;
                                    /**< 待处理文件关注链头 */
            struct comp_output_centre occ;
                                    /**< 压缩输出文件关注内容 */
        };
        /* 非压缩文件 */
        struct {
            uint8_t fc_type;       /**< dir/file */
            struct ft_file_centre fc;
        };
    };
};

/**
 * 上报服务器配置信息.
 */
struct ft_up_srv {
    uint8_t ipver : 1;                  /**< ipv4/v6 */
    uint8_t plat : 7;                   /**< 所属平台 */
    uint8_t up_mode;                    /**< 上报模式:sftp/ftp */
    uint16_t port;
    char user[FT_MAX_LEN_32];           /**< 用户名 */
    char pswd[FT_MAX_LEN_32];           /**< 密码 */
    char host[FT_MAX_LEN_32];           /**< 主机地址 */
    char path[FT_MAX_LEN_256];          /**< 上报路径 */
    char seg_url[FT_MAX_LEN_256];       /**< 用户不用关心，内部生成：固定url部分，如：sftp://*******:123/data */
};

/**
 * acquire线程处理流程truck承载结构.
 */
struct ft_acq_truck {
    void *acq;                           /**< acquire线程上下文 */
};

/**
 * compress线程处理流程truck承载结构.
 */
struct ft_comp_truck {
};

/**
 * upload线程处理流程truck承载结构.
 */
struct ft_up_truck {
    uint16_t tsf_type;                      /**< 当次处理所属tsf类型 */
    time_t cur_sec;                         /**< 当前时间sec */
    void *upload;                           /**< upload线程上下文 */
};

struct ft_up_platform {
#define FT_UP_SERVER_CNT_MAX        8
    uint8_t type;                           /**< 上报平台类型 */
    uint8_t up_srv_mode;                    /**< 上报服务器模式:disp/copy */
    uint8_t srv_cnt;                        /**< 服务器数，disp/copy有效 */
    char name[FT_MAX_LEN_64];               /**< 上报平台名 */
    struct ft_up_srv upsrv[FT_UP_SERVER_CNT_MAX];
                                            /**< 上报服务器配置，disp/copy有效 */
};

struct ft_file_sub_type {
    char str[FT_MAX_LEN_64];                /**< 子类型字符串 */
};

/**
 * transfer业务配置，含通用配置和私有配置.
 */
struct ft_tsf_cfg {
#define FT_UP_PLATFORM_CNT_MAX        16
#define FT_FILE_SUB_TYPE_MAX          8
    uint8_t plat_cnt;                       /**< 有效上报平台个数 */
    uint8_t compress_type;                  /**< 压缩类型 */
    uint8_t concern_sub;                    /**< 是否关心子类型 */
    char comp_path[FT_MAX_LEN_256];         /**< 压缩文件本地生成路径,支持更新 */
    char comp_pswd[FT_MAX_LEN_128 + 1];     /**< 压缩文件密码，最长128位，支持更新 */
    uint8_t plat_idx[FT_UP_PLATFORM_CNT_MAX];
                                            /**< 有效上报平台索引 */
    struct ft_up_platform platform[FT_UP_PLATFORM_CNT_MAX];
                                            /**< 上报平台,支持动态更新 */
    struct ft_file_sub_type sub[FT_FILE_SUB_TYPE_MAX];
                                            /**< 上报文件子类型 */
    void *priv;                             /**< 私有配置 */
};

/**
 * transfer通用流程统计信息，外部可用.
 */
struct ft_tsf_stat {
    uint64_t expect_ori_cnt;                /**< 处理的预期原始文件数 */
    uint64_t acq_duty_cnt;                  /**< acq通过duty资源获取的文件数 */
    uint64_t acq_abn_cnt;                   /**< acq通过abnormal获取的文件数 */
    uint64_t comp_duty_cnt;                 /**< comp通过duty资源获取的文件数 */
    uint64_t up_duty_cnt;                   /**< up通过duty资源获取的文件数 */
    uint64_t up_succeed_cnt;                /**< 上报成功文件数(disp/copy都1次) */
    uint64_t priv_get_err;                  /**< tsf priv池：内存获取失败计数 */
};

struct ft_transfer;
typedef int (*ft_tsf_init_f)(struct ft_transfer *tsf);
typedef int (*ft_tsf_hook_f)(struct ft_transfer *tsf,
                             struct ft_file_info *info,
                             void *attr);
typedef uint32_t (*ft_tsf_priv_ser_f)(const void *usr_priv,
                                      char *ser_buf,
                                      uint32_t ser_buf_len);
typedef int (*ft_tsf_priv_deser_f)(void *usr_priv,
                                    char *priv_str,
                                    uint32_t priv_str_len);
/**
 * transfer业务主结构，需注册使用.
 */
struct ft_transfer {
    uint8_t need_content;                       /**< 业务是否关心原始文件内容 */
    uint8_t name_len;                           /**< 类型名长度 */
    uint16_t type;                              /**< 传输文件类型 */
    uint32_t info_priv_sz;                      /**< fileinfo私有数据节点大小 */
    char name[FT_MAX_LEN_64];                   /**< 传输文件类型名 */
    struct ft_tsf_cfg cfg;                      /**< 配置(通用/私有) */
    pthread_rwlock_t rwlock;                    /**< 用于配置更改的读写锁 */
    ft_tsf_priv_ser_f ser;                      /**< fileinfo私有数据序列化接口 */
    ft_tsf_priv_deser_f deser;                  /**< fileinfo私有数据反序列化接口 */
    ft_tsf_init_f init;                         /**< 初始化 */
    ft_tsf_hook_f hook[FT_FILE_HOOK_MAX];       /**< 文件传输钩子 */
    void *info_priv_pool;                       /**< fileinfo私有数据内存池 */
    void *priv;                                 /**< transfer私有数据 */
};

/**
 * 未传输完成的异常记录文件信息.
 */
struct ft_abnormal_info {
    char file[FT_MAX_LEN_512];                  /**< 异常文件记录名 */
    uint64_t total_line;                        /**< 当前文件总行数 */
    uint64_t deal_line;                         /**< 当前文件已处理行数 */
};

/* 内存管理接口现在由 hmemory.h 提供，用户可以通过 hmem_allocator_set 设置自定义分配器 */

/**
 * 为output类线程向filetrans acquire线程注册创建责任线程资源.
 *
 * @param ring_sz
 *  创建队列大小，0表示使用内部默认队列大小，否则按ring_sz创建.
 * @param pool_node_cnt
 *  待处理文件内存池节点数，0表示使用内部默认内存池节点数，否则按pool_node_cnt创建.
 * @param th_name
 *  output线程名, 可以为空.
 * @return
 *  NULL: 创建失败.
 *  非NULL: 创建成功，需由output类线程保存，后续用于处理文件.
 */
void *ft_acquire_res_creat(uint32_t ring_sz, uint32_t pool_node_cnt, char *th_name);

/**
 * transfer注册接口
 *
 * @param tsf
 *  待注册业务transfer.
 * @return
 *  FT_OK: 注册成功.
 *  FT_FAIL: 注册失败.
 */
int ft_transfer_reg(struct ft_transfer *tsf);

/**
 * 开启filetrans文件传输服务.
 *
 * @param cfg
 *  用户配置服务参数.
 * @return
 *  FT_OK: 开启成功.
 *  FT_FAIL: 开启失败.
 */
int ft_start(const struct ft_cfg *cfg);

/**
 * output类线程调用该接口向acquire线程put文件信息.
 *
 * @param res
 *  output类线程向acquire线程申请注册的线程责任资源.
 *  传入NULL时，由内部公共资源处理(考虑性能，少量使用)
 * @param arg
 *  待处理文件信息.
 *      1. 文件或文件夹，eg. /home/<USER>/a.txt eg. /home/<USER>
 *      2. 文件列表信息，struct ft_file_info
 * @param attr
 *  文件相关属性，包括总行数，子类型等，可选
 * @param log_type
 *  具体类型文件对应transfer中定义的类型，必须已向filetrans注册过.
 * @param info_type
 *  标识当前待处理文件信息类型。dir/list/file
 * @return
 *  FT_OK: 输入成功.
 *  FT_FAIL: 输入失败.
 */
int ft_info_put_acq(void *res,
                    void *arg,
                    struct ft_info_attr *attr,
                    uint32_t log_type,
                    uint8_t info_type);

/**
 * 创建文件列表.
 *
 * @param log_type
 *  具体类型文件对应transfer中定义的类型，必须已向filetrans注册过.
 * @param out_res
 *  output类线程向acquire线程申请注册的线程责任资源
 *  传入NULL时，由内部公共资源处理(考虑性能，少量使用)
 * @return
 *  NULL: 创建失败.
 *  非NULL: 创建成功，并返回创建的文件列表.
 */
void *ft_file_list_creat(uint32_t log_type, void *out_res);

/**
 * 添加文件列表，常与ft_file_list_creat联用.
 *
 * @param file_list
 *  已创建的文件列表
 * @param path
 *  填加到文件列表的文件项全路径
 * @param out_res
 *  output类线程向acquire线程申请注册的线程责任资源
 *  传入NULL时，由内部公共资源处理(考虑性能，少量使用)
 * @param fail_free
 *  标识当前path添加失败，是否需要由内部回收整个文件列表
 * @return
 *  FT_OK: 添加成功.
 *  FT_FAIL: 添加失败.
 */
int ft_file_list_add(void *file_list, char *path, void *out_res, uint8_t fail_free);

/**
 * 上报tsf file文件.
 *
 * @param truck
 *  上报线程数据处理中间承载内容
 * @param srv
 *  待上报服务器
 * @param info
 *  待上报tsf file文件
 * @param url
 *  上报url
 * @return
 *  FT_OK: 上报成功.
 *  FT_FAIL: 上报失败.
 */
int ft_up_tsf_file(struct ft_up_truck *truck,
                   const struct ft_up_srv *srv,
                   struct ft_file_info *info,
                   char *url);
/**
 * 上报新文件.
 *
 * @param truck
 *  上报线程数据处理中间承载内容
 * @param srv
 *  待上报服务器
 * @param path
 *  待上报新文件全路径
 * @param url
 *  上报url
 * @param backup
 *  上报成功后是否备份：0-不备份，1-备份
 * @return
 *  FT_OK: 上报成功.
 *  FT_FAIL: 上报失败.
 */
int ft_up_new_file(struct ft_up_truck *truck,
                   const struct ft_up_srv *srv,
                   char *path,
                   char *url,
                   uint8_t backup);

/**
 * 服务器远端更名接口.
 *
 * @param truck
 *  上报线程数据处理中间承载内容
 * @param srv
 *  服务器
 * @param old_name
 *  旧名全路径
 * @param new_name
 *  新名全路径
 * @return
 *  FT_OK: 更名成功.
 *  FT_FAIL: 更名失败.
 */
int ft_up_file_name_change(struct ft_up_truck *truck,
                           const struct ft_up_srv *srv,
                           char *old_name,
                           char *new_name);

/**
 * 设置服务状态(未设置时，默认开启服务)
 *
 * @param status
 *  FT_FILETRANS_START:开启服务
 *  FT_FILETRANS_STOP:停止服务
 * @return
 */
void ft_status_set(uint8_t status);

/**
 * 获取服务状态
 *
 * @return
 *  FT_FILETRANS_START:服务开启状态
 *  FT_FILETRANS_STOP:服务停止状态
 */
uint8_t ft_status_get(void);

/**
 * 获取配置信息
 *
 * @return
 *  配置信息
 */
void *ft_cfg_get(void);

/**
 * 获取当前有效的transfer
 *
 * @param tsf
 *  用来存储有效transfer.
 * @param max
 *  tsf最大存储个数.
 * @param type
 *  tsf类型，-1时，表示获取所有tsf，否则获取指定类型的tsf
 * @return
 *  获取有效transfer个数
 */
uint32_t ft_transfer_get(struct ft_transfer **tsf, uint32_t max, int type);

/**
 * 获取当前异常处理文件信息
 *
 * @return
 *  异常处理文件信息
 */
void *ft_abnormal_info_get(void);

/**
 * 捕获某些信号的信号处理函数，主要处理已生成但未完成的文件信息，用户自行选择使用.
 *
 * @param sig
 *  信号值
 */
void ft_signal_dispose(int sig);

/**
 * 依据该info创建上报文件名.
 *
 * @param info
 *  待处理文件对应主结构.
 * @param name
 *  存储待创建文件名.
 * @param len
 *  name可用长度.
 * @return
 *  FT_OK: 创建成功.
 *  FT_FAIL: 创建失败.
 */
int ft_outfile_up_name_creat(struct ft_file_info *info, char *name, size_t len);

/**
 * 依据该info创建本地全路径文件名.
 *
 * @param info
 *  待处理文件对应主结构.
 * @param name
 *  存储待创建文件名.
 * @param len
 *  name可用长度.
 * @return
 *  FT_OK: 创建成功.
 *  FT_FAIL: 创建失败.
 */
int ft_outfile_local_name_creat(struct ft_file_info *info, char *name, size_t len);

/**
 * 线程用户私有数据设置
 *
 * @param arg
 *  用户参数
 * @param bind_core
 *  输出线程绑定核心, 0表示未绑定
 * @return
 *  NULL: 创建失败.
 *  非NULL: 创建成功,返回用户私有数据资源
 */
typedef void* (*ft_priv_thread_f)(void *arg, int bind_core);

/**
 * 设置acquire线程私有数据
 *
 * @param priv_fn
 *  用户私有数据创建回调
 * @param arg
 *  用户参数
 * @param idx
 *  用户私有数据索引
 * @return
 *  FT_OK: 设置成功.
 *  FT_FAIL: 设置失败.
 */
int ft_acq_priv_set(ft_priv_thread_f priv_fn, void *arg, int idx);

/**
 * 获取acquire线程私有数据
 *
 * @param acq
 *  acquire线程上下文
 * @param idx
 *  待获取用户私有数据索引
 * @return
 *  用户私有数据
 */
void *ft_acq_priv_get(void *acq, int idx);

/**
 * 设置upload线程私有数据
 *
 * @param priv_fn
 *  用户私有数据创建回调
 * @param arg
 *  用户参数
 * @param idx
 *  用户私有数据索引
 * @return
 *  FT_OK: 设置成功.
 *  FT_FAIL: 设置失败.
 */
int ft_upload_priv_set(ft_priv_thread_f priv_fn, void *arg, int idx);

/**
 * 获取upload线程私有数据
 *
 * @param upload
 *  acquire线程上下文
 * @param idx
 *  待获取用户私有数据索引
 * @return
 *  用户私有数据
 */
void *ft_upload_priv_get(void *upload, int idx);

/**
 * acquire线程用户私有数据设置
 *
 * @param arg
 *  用户参数
 * @param acq_core
 *  输出线程lcore, 0表示未绑定
 * @return
 *  NULL: 创建失败.
 *  非NULL: 创建成功
 */
typedef void* (*ft_acq_priv_set_f)(void *arg, int acq_core);

/**
 * 更新业务tsf配置信息
 *
 * @param tsf
 *  业务transfer.
 * @param new_cfg
 *  全新的配置信息.
 */
void ft_tsf_cfg_update(struct ft_transfer *tsf, struct ft_tsf_cfg *new_cfg);

/**
 * 更新运维配置信息
 *
 * @param new_cfg
 *  全新的配置信息.
 * @return
 *  FT_OK: 更新成功.
 *  FT_FAIL: 更新失败.
 */
int ft_ops_cfg_update(struct ft_ops_cfg *new_cfg);

/**
 * 上报平台srv初始化
 *
 * @param srv
 *  srv结构信息.
 * @param plat
 *  上报平台类型.
 */
void ft_tsf_plat_srv_init(struct ft_up_srv *srv, uint8_t plat);

typedef int (*ft_platform_up_f)(struct ft_transfer *tsf,
                                struct ft_file_info *info,
                                struct ft_up_srv *srv,
                                void *attr,
                                void *priv);
/**
 * tsf由对应平台内部选择服务器上报接口（线程安全）
 *
 * @param tsf
 *  业务tsf.
 * @param info
 *  待上报tsf file文件
 * @param attr
 *  线程truck参数
 * @param priv
 *  用户自定义数据
 * @param plat_up
 *  上报回调cb接口
 *
 * @return
 *  FT_OK: 上报成功.
 *  FT_FAIL: 上报失败.
 */
int ft_tsf_platform_upload(struct ft_transfer *tsf,
                           struct ft_file_info *info,
                           void *attr,
                           void *priv,
                           ft_platform_up_f plat_up);

/**
 * 获取输入文件名信息
 *
 * @param info
 *  上报输入的tsf file文件
 * @param filename
 *  [out]文件名
 * @param filename_len
 *  [out]文件名长度
 * @param concern_suf
 *  是否关心后缀名
 */
void ft_file_in_info_name_get(struct ft_file_info *info,
                              char **filename,
                              uint32_t *filename_len,
                              uint8_t concern_suf);

/**
 * 获取输入文件filestat信息
 *
 * @param info
 *  上报输入的tsf file文件
 * @return
 *  输入文件的filestat信息
 */
static inline struct stat *ft_file_in_info_filestat_get(struct ft_file_info *info)
{
    if (info->is_comp) {
        struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
        return &icc->filestat;
    } else {
        struct ft_file_centre *fc = &info->fc;
        return &fc->filestat;
    }
}

/**
 * 获取log_type类型tsf的fileinfo用户私有数据内存
 *
 * @param res
 *  output类线程向acquire线程申请注册的线程责任资源.
 *  传入NULL时，由内部公共资源处理(考虑性能，少量使用)
 * @param log_type
 *  具体类型文件对应transfer中定义的类型，必须已向filetrans注册过.
 * @return
 *  NULL: 获取失败.
 *  非NULL: 获取成功，返回可用内存.
 */
void *ft_file_info_priv_mem_get(void *res, uint32_t log_type);

/**
 * 创建tsf的fileinfo用户私有数据内存池
 *
 * @param tsf
 *  业务transfer.
 * @param priv_sz
 *  fileinfo对应的用户私有数据节点大小.
 * @param pool_node_cnt
 *  待处理文件内存池节点数，0表示使用内部默认内存池节点数，否则按pool_node_cnt创建.
 * @return
 *  FT_OK: 创建成功.
 *  FT_FAIL: 创建失败.
 */
int ft_file_info_priv_pool_creat(struct ft_transfer *tsf,
                                 uint32_t priv_sz,
                                 uint32_t pool_node_cnt);

/**
 * 开启多进程模式时，从进程初始化
 *
 * @return
 *  NULL: 初始化失败.
 *  非NULL: 初始化成功，返回共享内存地址.
 */
void *ft_mproc_slave_init(void);

/**
 * 开启多进程模式时，从进程退出前需反初始化
 *
 * @param msg
 *  共享内存地址.
 */
void ft_mproc_slave_deinit(struct ft_mproc_msg *msg);

/**
 * 开启多进程模式时，从进程开始操作共享内存
 *
 * @param msg
 *  共享内存地址.
 */
void ft_mproc_slave_msg_start(struct ft_mproc_msg *msg);

/**
 * 开启多进程模式时，从进程结束操作共享内存
 *
 * @param msg
 *  共享内存地址.
 */
void ft_mproc_slave_msg_stop(struct ft_mproc_msg *msg);

/**
 * 清空filetrans所有统计信息，包括tsf及线程资源统计信息
 */
void ft_statistics_clear_all(void);


/**
 * 获取类型为type的tsf的统计信息
 *
 * @param stat[out]
 *  待获取的统计信息
 * @param type
 *  tsf类型
 */
void ft_tsf_stat_get(struct ft_tsf_stat *stat, uint32_t type);

/**
 * 将filetrans线程资源信息输出到buf中
 *
 * @param buf
 *  信息输出buf
 * @param buf_sz
 *  buf大小
 * @param type
 *  线程类型
 * @return
 *  实际写入buf长度
 */
uint32_t ft_res_info_dump(char *buf, uint32_t buf_sz, enum ft_thread_type type);

/**
 * 获取上报线程上报速率相关统计
 *
 * @param KBps
 *  每秒上传字节数,单位KBps
 * @param fps
 *  每秒上传文件数
 * @param up_idx
 *  获取的上报线程索引
 */
void ft_up_stat_get(double *KBps, double *fps, uint8_t up_idx);

/**
 * 更新上报线程滑动窗口统计数据
 *
 * @param up_idx
 *  上报线程索引
 * @param bytes
 *  本次上报字节数
 * @param files
 *  本次上报文件数
 */
void ft_up_stat_update(uint8_t up_idx, size_t bytes, uint64_t files);

/**
 * 初始化上报线程统计数据
 *
 * @param up_idx
 *  上报线程索引
 */
void ft_up_stat_init(uint8_t up_idx);

/**
 * 获取压缩线程压缩性能相关统计
 *
 * @param compress_rate_KBps
 *  输出参数：压缩速率（KB/s，基于输入数据）
 * @param fps
 *  输出参数：文件处理速率（files/s）
 * @param compression_ratio
 *  输出参数：平均压缩比率
 * @param comp_idx
 *  压缩线程索引
 * @return
 *  FT_OK: 查询成功
 *  FT_FAIL: 查询失败
 */
int ft_comp_stat_get(double *compress_rate_KBps, double *fps, 
                     double *compression_ratio, uint8_t comp_idx);

/**
 * 更新压缩线程滑动窗口统计数据
 *
 * @param comp_idx
 *  压缩线程索引
 * @param input_bytes
 *  输入数据字节数
 * @param output_bytes
 *  输出数据字节数
 * @param files
 *  处理的文件数
 * @return
 *  FT_OK: 更新成功
 *  FT_FAIL: 更新失败
 */
int ft_comp_stat_update(uint8_t comp_idx, size_t input_bytes, 
                        size_t output_bytes, uint64_t files);

/**
 * 初始化压缩线程统计数据
 *
 * @param comp_idx
 *  压缩线程索引
 * @return
 *  FT_OK: 初始化成功
 *  FT_FAIL: 初始化失败
 */
int ft_comp_stat_init(uint8_t comp_idx);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FILETRANS_H__ */
