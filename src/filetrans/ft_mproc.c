#include "ft_mproc.h"

static void *ft_mproc_master_mem_get(size_t total_sz)
{
    int fd;

    /* 打开共享内存文件 */
    fd = open(FT_MPROC_SHM_FILE, O_RDWR | O_CREAT, 0666);
    if (fd < 0) {
        FT_ERROR("master: %s open failed, fd = %d\n", FT_MPROC_SHM_FILE, fd);
        return NULL;
    }

    /* 修改fd指定文件的大小 */
    if (ftruncate(fd, total_sz) < 0) {
        close(fd);
        FT_ERROR("%s ftruncate err\n", FT_MPROC_SHM_FILE);
        return NULL;
    }

    /* 映射共享内存 */
    void *mem = mmap(NULL, total_sz, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (mem == MAP_FAILED) {
        close(fd);
        return NULL;
    }
    memset(mem, 0, total_sz);
    close(fd);

    return mem;
}

static void *ft_mproc_slave_mem_get(void)
{
    int fd;
    struct ft_mproc_msg *mp_msg;

    /* 打开共享内存文件 */
    fd = open(FT_MPROC_SHM_FILE, O_RDWR, 0666);
    if (fd < 0) {
        FT_ERROR("slave: %s open failed, fd = %d\n", FT_MPROC_SHM_FILE, fd);
        return NULL;
    }

    /* 映射共享内存 */
    mp_msg = mmap(NULL, sizeof(*mp_msg), PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (mp_msg == MAP_FAILED) {
        close(fd);
        return NULL;
    }

    if (mp_msg->usr_priv_sz) {
        /* 获取主进程映射内存大小后，解除旧映射，重新映射 */
        size_t total_sz = sizeof(*mp_msg) + mp_msg->usr_priv_sz;
        munmap(mp_msg, sizeof(*mp_msg));
        mp_msg = mmap(NULL, total_sz, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
        if (mp_msg == MAP_FAILED) {
            close(fd);
            return NULL;
        }
    }

    close(fd);
    return mp_msg;
}

static size_t ft_mproc_tsf_info_priv_sz_max_get(void)
{
    uint16_t i;
    size_t max = 0;
    struct ft_transfer *tsf;
    struct ft_ctx *ctx = &g_ft_ctx;

    for (i = 0; i < ctx->tsf_cnt; i++) {
        tsf = ctx->tsf[ctx->tsf_idx[i]];
        if (!tsf->info_priv_pool)
            continue;
        if (tsf->info_priv_sz > max)
            max = tsf->info_priv_sz;
    }

    return max;
}

void *ft_mproc_master_init(void)
{
    struct ft_mproc_msg *mp_msg;
    size_t max_tsf_priv_sz = ft_mproc_tsf_info_priv_sz_max_get();
    size_t total_sz = sizeof(*mp_msg) + max_tsf_priv_sz;

    mp_msg = ft_mproc_master_mem_get(total_sz);
    if (!mp_msg)
        return NULL;

    mp_msg->produced = 0;       //未生产
    mp_msg->consumed = 1;       //已消费
    mp_msg->usr_priv_sz = max_tsf_priv_sz;
    return mp_msg;
}

void *ft_mproc_slave_init(void)
{
    return ft_mproc_slave_mem_get();
}

void ft_mproc_slave_deinit(struct ft_mproc_msg *msg)
{
    if (!msg)
        return;
    munmap(msg, sizeof(*msg) + msg->usr_priv_sz);
}

uint8_t ft_mproc_master_msg_start(struct ft_mproc_msg *msg)
{
    return msg->produced;
}

void ft_mproc_master_msg_stop(struct ft_mproc_msg *msg)
{
    msg->produced = 0;
    msg->consumed = 1;
}

void ft_mproc_slave_msg_start(struct ft_mproc_msg *msg)
{
    while (!msg->consumed) {
        usleep(10);   //等待完全消费完成
    }
}

void ft_mproc_slave_msg_stop(struct ft_mproc_msg *msg)
{
    msg->consumed = 0;
    msg->produced = 1;
}
