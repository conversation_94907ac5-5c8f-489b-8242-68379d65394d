#include <sys/types.h>
#include <dirent.h>
#include "ft.h"

struct ft_upload_centre {
    uint32_t send_len;              /**< 已上报发送长度 */
    uint32_t buf_sz;                /**< 实际长度 */
    void *buf;                      /**< 实际内容 */
};

static uint8_t ft_get_up_sig(struct ft_up_thread *up)
{
    return up->sig_flag;
}

/* 基于各线程内的分发 */
static void *ft_up_srv_disp_choose(struct ft_up_truck *truck,
                                   struct ft_up_platform *plat,
                                   uint16_t tsf_type)
{
#define FT_UPLOAD_DISP_BASE_UP_CNT_MAX         1000000
    struct ft_up_thread *up = truck->upload;
    struct ft_up_disp_base *disp = &up->disp[tsf_type];

    if (unlikely(disp->num[plat->type] >= FT_UPLOAD_DISP_BASE_UP_CNT_MAX))
        disp->num[plat->type] = 0;
    uint32_t srv_idx = (disp->num[plat->type]++) % plat->srv_cnt;
    return &plat->upsrv[srv_idx];
#undef FT_UPLOAD_DISP_BASE_UP_CNT_MAX
}

int ft_up_tsf_file(struct ft_up_truck *truck,
                   const struct ft_up_srv *srv,
                   struct ft_file_info *info,
                   char *url)
{
    struct ft_up_thread *up = truck->upload;

    if (unlikely(ft_get_up_sig(up)))    //捕捉到信号，立即结束
        return FT_FAIL;

    if (unlikely(!srv || !url))
        return FT_FAIL;

    uint32_t cnt = FT_UP_TRY_CNT_MAX;
    struct ft_mode_up *mu = &up->mu[srv->up_mode];

    FT_DEBUG("Start upload file %s to %s\n", ft_get_info_path(info), url);
    /* 尝试上报FT_UP_TRY_CNT_MAX次，上报成功立即结束 */
    while (cnt--) {
        if (FT_OK == mu->up_data(up, srv, info, url)) {
            FT_DEBUG("End upload file %s to %s (success!)\n", ft_get_info_path(info),
                    url);
            return FT_OK;
        }
    }
    FT_DEBUG("End upload file %s to %s (fail!)\n", ft_get_info_path(info), url);

    return FT_FAIL;
}

int ft_up_new_file(struct ft_up_truck *truck,
                   const struct ft_up_srv *srv,
                   char *path,
                   char *url,
                   uint8_t backup)
{
    struct ft_file_info info;
    size_t len = strlen(path);
    if (unlikely(len >= sizeof(info.fc.path))) {
        FT_ERROR("path(%s) is too lang\n", path);
        return FT_FAIL;
    }

    info.is_comp = 0;
    ft_file_centre_init(&info.fc);
    if (-1 == stat(path, &info.fc.filestat)) {
        FT_ERROR("%s get stat err\n", path);
        return FT_FAIL;
    }
    memcpy(info.fc.path, path, len);
    info.fc.path[len] = 0;

    int ret = ft_up_tsf_file(truck, srv, &info, url);
    if (backup) {
        FT_OPS_NEW_INFO_SET(&info);         /* 标识是新建文件 */
        info.attr.platform = srv->plat;     /* 平台保持一致 */
        info.type = truck->tsf_type;
        ft_nor_file_or_dir_ops(&info, truck->cur_sec);
    }

    return ret;
}

/*
 * 检查所有upload线程是否均上报完成（信号异常处理时使用）
 */
uint8_t ft_task_all_done(void)
{
    uint32_t i;
    for (i = 0; i < g_ft_ctx.cfg.up_cnt; i++) {
        if (!g_ft_ctx.up[i].done)
            return 0;
    }

    return 1;
}

/* upload通过duty资源获取的文件数 */
static inline void ft_tsf_stat_up_duty_inc(struct ft_up_thread *up, uint32_t type)
{
    struct ft_up_tsf_stat *stat = &up->tsf_stat[type];
    stat->up_duty_cnt++;
}

/* upload上报成功的次数（dispatch 1次，copy方式任一成功也计1次） */
static inline void ft_tsf_stat_up_succeed_inc(struct ft_up_thread *up, uint32_t type)
{
    struct ft_up_tsf_stat *stat = &up->tsf_stat[type];
    stat->up_succeed_cnt++;
}

static inline int ft_up_curl_reset(CURL *curl)
{
    /* 允许创建不存在的目录、禁止验证对等证书、设置上传功能、设置超时功能 */
    curl_easy_setopt(curl, CURLOPT_FTP_CREATE_MISSING_DIRS, 1);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L); //禁用ssl验证
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L); //禁用ssl验证
    curl_easy_setopt(curl, CURLOPT_UPLOAD, 1L);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);

    /* 设置连接超时时间 2s */
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 2L);
    /* 设置curl最长执行时间 10s */
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    return FT_OK;
}

#ifdef DEBUG
/* 获取上传服务信息 */
void ft_up_curl_info_get(CURL *curl)
{
#if (LIBCURL_VERSION_MAJOR >= 7 && LIBCURL_VERSION_MINOR >= 55)
    CURLcode res;
    curl_off_t val;

    /* 打印上传数据量的总值 */
    res = curl_easy_getinfo(curl, CURLINFO_SIZE_UPLOAD_T, &val);
    if ((CURLE_OK == res) && (val > 0))
        FT_DEBUG("Data upload: %lu bytes.\n", (uint64_t)val);

    /* 打印平均上传速度 */
    res = curl_easy_getinfo(curl, CURLINFO_SPEED_UPLOAD_T, &val);
    if ((CURLE_OK == res) && (val > 0))
        FT_DEBUG("Average upload speed: %lu kb/s.\n", (uint64_t)(val / 1024));

#if (LIBCURL_VERSION_MINOR >= 61)
    /* 打印最后一次传输所消耗的时间 */
    res = curl_easy_getinfo(curl, CURLINFO_TOTAL_TIME_T, &val);
    if ((CURLE_OK == res) && (val > 0)) {
        FT_DEBUG("Total upload time: %lu.%06lu s.\n", (uint64_t)(val / 1000000),
                  (uint64_t)(val % 1000000));
    }

    /* 打印名称解析所消耗的时间 */
    res = curl_easy_getinfo(curl, CURLINFO_NAMELOOKUP_TIME_T, &val);
    if ((CURLE_OK == res) && (val > 0)) {
        FT_DEBUG("Name lookup time: %lu.%06lu s.\n", (uint64_t)(val / 1000000),
                (uint64_t)(val % 1000000));
    }

    /* 打印建立连接所消耗的时间 */
    res = curl_easy_getinfo(curl, CURLINFO_CONNECT_TIME_T, &val);
    if ((CURLE_OK == res) && (val > 0)) {
        FT_DEBUG("Connect time: %lu.%06lu s.\n", (uint64_t)(val / 1000000),
                (uint64_t)(val % 1000000));
    }
#endif
#endif
}
#endif

static inline void ft_up_data_curl_init(CURL *curl,
                                        const struct ft_up_srv *srv,
                                        char *url,
                                        uint8_t cmd_mode)
{
    /* 额外设置ipv6标识 */
    if (FT_IP_VER6 == srv->ipver)
        curl_easy_setopt(curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V6);

    /* 设置用户名/密码/url */
    curl_easy_setopt(curl, CURLOPT_USERNAME, srv->user);
    curl_easy_setopt(curl, CURLOPT_PASSWORD, srv->pswd);
    curl_easy_setopt(curl, CURLOPT_URL, url);

    if (FT_UPLOAD_SFTP == srv->up_mode)
        curl_easy_setopt(curl, CURLOPT_SSH_AUTH_TYPES, CURLSSH_AUTH_PASSWORD);

    if (cmd_mode) {
        /* 命令模式时，不输出调试信息+主动传输+只获取响应头部 */
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
        curl_easy_setopt(curl, CURLOPT_FTP_USE_EPSV, 0L);
        curl_easy_setopt(curl, CURLOPT_NOBODY, 1L);
    }
}

int ft_up_name_change_sftp(CURL *curl,
                           const struct ft_up_srv *srv,
                           char *old_name,
                           char *new_name)
{
    char *old = old_name;
    char *new = new_name;
    char rename_cmd[FT_MAX_LEN_512];
    struct curl_slist *quote_list = NULL;

    /* *
     * 构造重命名命令.
     * @note:
     *  sftp对文件名会进行严格检查，不支持对"~"进行家目录转换;
     *  故这里需要检查替换;
     */

    if (*old == '~') {
        old++;
        while (*old == '/')
            old++;
    }

    if (*new == '~') {
        new++;
        while (*new == '/')
            new++;
    }
    snprintf(rename_cmd, sizeof(rename_cmd), "rename %s %s", old, new);

    quote_list = curl_slist_append(quote_list, rename_cmd);
    curl_easy_setopt(curl, CURLOPT_POSTQUOTE, quote_list);

    /* 结果执行 */
    int ret;
    CURLcode res = curl_easy_perform(curl);
    if (CURLE_OK != res) {
        ret = FT_FAIL;
        FT_ERROR("url(%s): (%s) err: %s\n", srv->seg_url, rename_cmd,
                curl_easy_strerror(res));
    } else {
        ret = FT_OK;
        FT_DEBUG("url(%s): (%s) success\n", srv->seg_url, rename_cmd);
    }

    curl_easy_reset(curl);
    ft_up_curl_reset(curl);
    curl_slist_free_all(quote_list);

    return ret;
}

int ft_up_name_change_ftp(CURL *curl,
                          const struct ft_up_srv *srv,
                          char *old_name,
                          char *new_name)
{
    char rnfr_cmd[FT_MAX_LEN_256];
    char rnto_cmd[FT_MAX_LEN_256];
    struct curl_slist *quote_list = NULL;

    /* 构造重命名命令 */
    snprintf(rnfr_cmd, sizeof(rnfr_cmd), "RNFR %s", old_name);
    quote_list = curl_slist_append(quote_list, rnfr_cmd);
    snprintf(rnto_cmd, sizeof(rnto_cmd), "RNTO %s", new_name);
    quote_list = curl_slist_append(quote_list, rnto_cmd);
    curl_easy_setopt(curl, CURLOPT_POSTQUOTE, quote_list);

    /* 结果执行 */
    int ret;
    CURLcode res = curl_easy_perform(curl);
    if (CURLE_OK != res) {
        ret = FT_FAIL;
        FT_ERROR("url(%s): (%s) to (%s) err: %s\n", srv->seg_url, rnfr_cmd, rnto_cmd,
                curl_easy_strerror(res));
    } else {
        ret = FT_OK;
        FT_DEBUG("url(%s): (%s) to (%s) success\n", srv->seg_url, rnfr_cmd, rnto_cmd);
    }

    curl_easy_reset(curl);
    ft_up_curl_reset(curl);
    curl_slist_free_all(quote_list);

    return ret;
}

int ft_up_file_name_change(struct ft_up_truck *truck,
                           const struct ft_up_srv *srv,
                           char *old_name,
                           char *new_name)
{
    if (unlikely(!srv))
        return FT_FAIL;
    struct ft_up_thread *up = truck->upload;
    struct ft_mode_up *mu = &up->mu[srv->up_mode];

    /* curl初始化 */
    curl_easy_reset(up->curl);
    ft_up_data_curl_init(up->curl, srv, (char*)srv->seg_url, 1);

    return mu->change_name(up->curl, srv, old_name, new_name);
}

/* curl回调函数 */
size_t ft_up_read_cb(void *ptr, size_t size, size_t nmemb, void *stream)
{
    struct ft_upload_centre *pst = stream;
    if (unlikely(!pst->buf))
        return 0;

    size_t copy_size = size * nmemb;
    if (copy_size < 1)
        return 0;

    /* 逐步拷贝数据 */
    size_t rem_sz = pst->buf_sz - pst->send_len;
    size_t copy_len = (copy_size <= rem_sz) ? copy_size : rem_sz;
    if (copy_len) {
        memcpy(ptr, (char *)(pst->buf) + pst->send_len, copy_len);
        pst->send_len += copy_len;
    }

    return copy_len;
}

/* 压缩文件上报 */
static inline int ft_up_data_comp(CURL *curl, struct ft_file_info *info, char *url)
{
    struct ft_upload_centre cb_data = {
        .send_len = 0,
        .buf_sz = info->occ.buf_sz,
        .buf = info->occ.buf
    };

    /* 设置回调处理buf数据 */
    curl_easy_setopt(curl, CURLOPT_READDATA, &cb_data);
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, ft_up_read_cb);
    curl_easy_setopt(curl, CURLOPT_INFILESIZE_LARGE, (curl_off_t)cb_data.buf_sz);

    /* 上报结果 */
    CURLcode res = curl_easy_perform(curl);
    if (CURLE_OK != res) {
        FT_DEBUG("upload %s(flag %u) to %s err: %s\n", ft_get_info_path(info),
                info->icc_hdr.type, url, curl_easy_strerror(res));
        return FT_FAIL;
    }

    FT_DEBUG("upload %s(flag %u) to %s success\n", ft_get_info_path(info),
            info->icc_hdr.type, url);

    return FT_OK;
}

/* 原始文件上报 */
static inline int ft_up_data_ori(CURL *curl, char *file, size_t file_sz, char *url)
{
    FILE *fp;
    fp = fopen(file, "r");
    if (!fp) {
        FT_ERROR("%s open err\n", file);
        return FT_FAIL;
    }

    curl_easy_setopt(curl, CURLOPT_READFUNCTION, NULL);
    curl_easy_setopt(curl, CURLOPT_READDATA, fp);
    curl_easy_setopt(curl, CURLOPT_INFILESIZE, file_sz);

    /* 上报结果 */
    CURLcode res = curl_easy_perform(curl);
    if (CURLE_OK != res) {
        fclose(fp);
        FT_DEBUG("upload %s to %s err: %s\n", file, url, curl_easy_strerror(res));
        return FT_FAIL;
    }
    fclose(fp);

    FT_DEBUG("upload %s to %s success\n", file, url);

    return FT_OK;
}

int __ft_upload_data(struct ft_up_thread *up,
                     const struct ft_up_srv *srv,
                     struct ft_file_info *info,
                     char *url)
{
    int ret;
    CURL *curl = up->curl;

    /* curl初始化 */
    ft_up_data_curl_init(curl, srv, url, 0);
    /* 上报失败立即结束 */
    if (!info->is_comp) {
        /* 上报原始内容 */
        ret = ft_up_data_ori(curl, info->fc.path, info->fc.filestat.st_size, url);
        FT_ERR_CHK_RET(ret);
    } else {
        if (!info->occ.buf) {
            /* 上报压缩内容(基于文件流) */
            char file_name[FT_MAX_LEN_256];
            ft_outfile_local_name_creat(info, file_name, sizeof(file_name));
            ret = ft_up_data_ori(curl, file_name, info->occ.buf_sz, url);
        } else {
            /* 上报压缩内容(基于内存) */
            ret = ft_up_data_comp(curl, info, url);
        }
        FT_ERR_CHK_RET(ret);
    }

    size_t upload_bytes;
    if (!info->is_comp) {
        upload_bytes = info->fc.filestat.st_size;
    } else {
        upload_bytes = info->occ.buf_sz;
    }

    /* 更新滑动窗口统计 */
    ft_up_stat_update(up->th_idx, upload_bytes, 1);

#ifdef DEBUG
    /* 上传成功获取上传统计信息 */
    ft_up_curl_info_get(curl);
#endif

    return FT_OK;
}

int ft_upload_data_sftp(struct ft_up_thread *up,
                        const struct ft_up_srv *srv,
                        struct ft_file_info *info,
                        char *url)
{
    return __ft_upload_data(up, srv, info, url);
}

int ft_upload_data_ftp(struct ft_up_thread *up,
                       const struct ft_up_srv *srv,
                       struct ft_file_info *info,
                       char *url)
{
    return __ft_upload_data(up, srv, info, url);
}

static inline void __up_truck_init(struct ft_up_thread *up,
                                   time_t cur,
                                   struct ft_transfer *tsf,
                                   struct ft_up_truck *truck)
{
    truck->upload = up;
    truck->cur_sec = cur;
    truck->tsf_type = tsf->type;
}

static inline int __up_data_dispose(struct ft_up_thread *up,
                                    struct ft_transfer *tsf,
                                    struct ft_file_info *info,
                                    time_t cur)
{
    struct ft_up_truck truck;
    __up_truck_init(up, cur, tsf, &truck);

    /* 上报hook流程 */
    int ret = tsf->hook[FT_FILE_UPLOAD](tsf, info, &truck);
    if (ret != FT_OK) {
        FT_DEBUG("info %s: hook upload err\n", ft_get_info_path(info));
    }

    return ret;
}

static void ft_up_data_dispose(struct ft_up_thread *up,
                               struct ft_file_info *info,
                               time_t cur)
{
    struct ft_transfer *tsf = up->tsf[info->type];

    FT_DEBUG("get path = %s\n", ft_get_info_path(info));
    ft_tsf_stat_up_duty_inc(up, info->type);
    /* 上报成功立即回收+计数+运维，失败则处理异常 */
    if (FT_OK == __up_data_dispose(up, tsf, info, cur)) {
        ft_tsf_stat_up_succeed_inc(up, info->type);
        ft_nor_file_or_dir_ops(info, cur);   // 备份/删除
        ft_file_info_obj_put(info);
    } else {
        ft_comp_file_remove(info);      //上报失败，删除压缩文件
        ft_abnormal_info_put(info);
    }
}

static void ft_upload_res_converge(struct ft_up_thread *up,
                                   struct ft_duty_res **duty_res,
                                   uint8_t *duty_cnt)
{
    int i;
    uint8_t cnt = 0;

    duty_res[cnt++] = &up->acq_res;

    for (i = 0; i < up->duty_comp_cnt; i++) {
        duty_res[cnt++] = up->comp_res[i];
    }
    *duty_cnt = cnt;
}

void *ft_upload_process(void *arg)
{
#define FT_UP_IDLE_CNT_MAX     1000
    struct ft_up_thread *up = arg;

    /* 设置亲和性/线程名 */
    if (FT_OK != ft_thread_priv_attr_set(up->bind_core, up->name)) {
        FT_EXIT(-1);
    }

    int i;
    uint8_t duty_cnt;
    struct timespec tm;
    struct ft_file_info *info;
    struct ft_duty_res *duty_res[FT_THREAD_MAX + 1];

    /* 整合acq_res/comp_res */
    ft_upload_res_converge(up, duty_res, &duty_cnt);
    while (1) {
        clock_gettime(CLOCK_REALTIME_COARSE, &tm);
        /* 原始文件或经压缩文件上报处理 */
        for (i = 0; i < duty_cnt; i++) {
            if (FT_OK != ft_res_entry_get(duty_res[i], (void**)&info)) {
                up->idle++;
                continue;
            }
            up->idle = 0;
            ft_up_data_dispose(up, info, tm.tv_sec);
        }

        /* 空闲次数达到FT_UP_IDLE_CNT_MAX时，若捕捉到信号，设置上报完成 */
        if (up->idle >= FT_UP_IDLE_CNT_MAX) {
            up->idle = 0;
            if (unlikely(ft_get_up_sig(up))) {
                up->done = 1;
            }
        }
    }

    return NULL;
}

static int ft_thread_upload_acq_res_creat(struct ft_up_thread *up, int idx)
{
#define FT_UP_RING_SZ_DEF      65536
    uint32_t r_sz = FT_UP_RING_SZ_DEF;
    char name[FT_MAX_LEN_64];
    struct ft_duty_res *res;

    res = &up->acq_res;

    /* 创建单生产单消费类型队列 */
    snprintf(name, sizeof(name), "up_duty_acq_ring_%d", idx);
    /* 对接acq线程队列长度默认1024即可 */
    res->ring = rte_ring_create(name, r_sz, SOCKET_ID_ANY,
                                RING_F_SP_ENQ | RING_F_SC_DEQ);
    if (!res->ring) {
        FT_ERROR("rte_ring_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }
    /* acq -> === -> upload*/
    ft_thread_res_name_set(res, up->name, 0);
    ft_thread_res_name_set(res, FT_ACQ_THREAD_NAME, 1);

    return FT_OK;
}

static int ft_thread_upload_comp_assign(struct ft_ctx *ctx)
{
    struct ft_cfg *cfg = &ctx->cfg;

    /* 未开启压缩线程时，无需关联 */
    if (!ctx->comp_enable)
        return FT_OK;

    /* 压缩线程不可少于上报线程数 */
    if (cfg->comp_cnt < cfg->up_cnt)
        return FT_FAIL;

    int i, idx = 0;
    struct ft_up_thread *up;
    struct ft_comp_thread *comp;

    /* 均分线程资源 */
    for (i = 0; i < cfg->comp_cnt; i++) {
        if (idx == cfg->up_cnt)
            idx = 0;
        up = &ctx->up[idx];
        comp = &ctx->comp[i];
        /* comp -> === -> upload*/
        ft_thread_res_name_set(&comp->self_res, up->name, 0);
        up->comp_res[up->duty_comp_cnt++] = &comp->self_res;
        idx++;
    }

    return FT_OK;
}

static int ft_up_curl_creat(struct ft_up_thread *up)
{
    up->curl = curl_easy_init();
    if (!up->curl)
        return FT_FAIL;
    //curl_easy_setopt(up->curl, CURLOPT_VERBOSE, 1L);
    return ft_up_curl_reset(up->curl);
}

static inline int ft_curl_init(void)
{
    CURLcode res = curl_global_init(CURL_GLOBAL_ALL);
    if (CURLE_OK != res) {
        FT_ERROR("curl global init err\n");
        return FT_FAIL;
    }
    return FT_OK;
}

static int ft_thread_upload_early_init(struct ft_ctx *ctx)
{
    int ret;
    int i;
    struct ft_up_thread *up;
    struct ft_cfg *cfg = &ctx->cfg;

    /* 全局初始化libcurl */
    ret = ft_curl_init();
    FT_ERR_CHK_RET(ret);

    for (i = 0; i < cfg->up_cnt; i++) {
        up = &ctx->up[i];
        up->th_idx = i;
        up->bind_core = cfg->upload_core[i];
        up->mu = ctx->mu;
        up->tsf = ctx->tsf;
        snprintf(up->name, sizeof(up->name), "ft_upload_%u", up->th_idx);

        /* 初始化滑动窗口统计 */
        ft_up_stat_init(i);

        /* 创建curl上报器 */
        ret = ft_up_curl_creat(up);
        FT_ERR_CHK_RET(ret);

        /* 创建责任acq资源 */
        ret = ft_thread_upload_acq_res_creat(up, i);
        FT_ERR_CHK_RET(ret);
    }

    /* 为upload分配comp责任线程 */
    ret = ft_thread_upload_comp_assign(ctx);
    FT_ERR_CHK_RET(ret);

    ctx->up_init = 1;
    return FT_OK;
}

static inline void ft_upload_mode_creat(struct ft_ctx *ctx)
{
    struct ft_mode_up *mu;

    /* sftp mode */
    mu = &ctx->mu[FT_UPLOAD_SFTP];
    mu->up_data = ft_upload_data_sftp;
    mu->change_name = ft_up_name_change_sftp;

    /* ftp mode */
    mu = &ctx->mu[FT_UPLOAD_FTP];
    mu->up_data = ft_upload_data_ftp;
    mu->change_name = ft_up_name_change_ftp;
}

int ft_thread_upload_creat(struct ft_ctx *ctx)
{
    int i;
    int ret;
    pthread_t tid;
    struct ft_cfg *cfg = &ctx->cfg;

    /* 各模式上报接口注册创建 */
    ft_upload_mode_creat(ctx);

    /* 线程早期资源初始化 */
    ret = ft_thread_upload_early_init(ctx);
    FT_ERR_CHK_RET(ret);

    struct ft_up_thread *up;
    for (i = 0; i < cfg->up_cnt; i++) {
        up = &ctx->up[i];
        ret = pthread_create(&tid, NULL, ft_upload_process, up);
        if (ret < 0) {
            FT_ERROR("filetrans upload thread[idx:%d, lcore:%u] create error\n",
                    i, up->bind_core);
            return FT_FAIL;
        } else {
            FT_INFO("filetrans upload thread[idx:%d, lcore:%u] create\n",
                    i, up->bind_core);
            pthread_detach(tid);
        }
    }

    return FT_OK;
}

int ft_upload_priv_set(ft_priv_thread_f priv_fn, void *arg, int idx)
{
    if (idx >= FT_UP_PRIV_MAX)
        return FT_FAIL;

    uint32_t i;
    struct ft_up_thread *up;
    struct ft_cfg *cfg = &g_ft_ctx.cfg;

    for (i = 0; i < cfg->up_cnt; i++) {
        up = &g_ft_ctx.up[i];
        up->priv[idx] = priv_fn(arg, cfg->upload_core[i]);
        if (!up->priv[idx])
            return FT_FAIL;
    }

    return FT_OK;
}

void *ft_upload_priv_get(void *up, int idx)
{
    return ((struct ft_up_thread*)up)->priv[idx];
}

static int __ft_tsf_platform_upload_dir(struct ft_transfer *tsf,
                                        struct ft_file_info *info,
                                        struct ft_up_srv *srv,
                                        void *truck,
                                        void *priv,
                                        ft_platform_up_f plat_up)
{
    DIR *dir = opendir(info->fc.path);
    if (!dir) {
        return FT_FAIL;
    }

    struct dirent *entry;
    struct ft_file_info new_info;
    struct ft_file_centre *fc = &new_info.fc;

    new_info.is_comp = 0;
    new_info.type = info->type;
    new_info.res = NULL;
    char path[FT_MAX_LEN_512];

    while ((entry = readdir(dir))) {
        /* 过滤目录，即子目录不处理 */
        if (DT_DIR == entry->d_type)
            continue;

        snprintf(path, sizeof(path), "%s/%s", info->fc.path, entry->d_name);
        if (FT_OK != file_info_path_parse(fc, path)) {
            FT_ERROR("upload dir %s: info_path parse %s err\n", info->fc.path, fc->path);
            return FT_FAIL;
        }

        stat(fc->path, &fc->filestat);
        if (FT_OK != plat_up(tsf, &new_info, srv, truck, priv)) {
            FT_ERROR("upload dir %s: up %s err\n", info->fc.path, fc->path);
            return FT_FAIL;
        }
    }

    return FT_OK;
}

int ft_tsf_platform_upload(struct ft_transfer *tsf,
                           struct ft_file_info *info,
                           void *truck,
                           void *priv,
                           ft_platform_up_f plat_up)
{
    if (unlikely(!plat_up))
        return FT_FAIL;

    int ret = FT_FAIL;
    struct ft_up_platform *ori_platform = &tsf->cfg.platform[info->attr.platform];
    struct ft_up_platform platform;

    pthread_rwlock_rdlock(&tsf->rwlock);
    memcpy(&platform, ori_platform, sizeof(platform));
    pthread_rwlock_unlock(&tsf->rwlock);

    if (unlikely(!platform.srv_cnt)) {
        FT_ERROR("srv_cnt is empty\n");
        return FT_FAIL;
    }

    struct ft_up_srv *srv;
    if (platform.up_srv_mode == FT_UP_SRV_DISP) {
        srv = ft_up_srv_disp_choose(truck, &platform, tsf->type);
        if (info->is_comp || info->fc_type != FT_INPUT_DIR) {
            ret = plat_up(tsf, info, srv, truck, priv);
        } else {
            ret = __ft_tsf_platform_upload_dir(tsf, info, srv, truck, priv, plat_up);
        }
    } else {
        int i;
        if (info->is_comp || info->fc_type != FT_INPUT_DIR) {
            for (i = 0; i < platform.srv_cnt; i++) {
                srv = &platform.upsrv[i];
                /* 任一服务器上传成功即整体成功,其中未上传成功的日志会有记录 */
                if (FT_OK == plat_up(tsf, info, srv, truck, priv))
                    ret = FT_OK;
            }
        } else {
            for (i = 0; i < platform.srv_cnt; i++) {
                srv = &platform.upsrv[i];
                /* 任一服务器上传成功即整体成功,其中未上传成功的日志会有记录 */
                if (FT_OK == __ft_tsf_platform_upload_dir(tsf, info, srv, truck,
                            priv, plat_up)) {
                    ret = FT_OK;
                }
            }
        }
    }

    return ret;
}
