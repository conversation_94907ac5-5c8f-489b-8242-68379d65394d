#ifndef __FT_OPS_H__
#define __FT_OPS_H__

#include "filetrans.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define FT_SYS_WAIT_CLD         0
#define FT_OPS_ONE_DAY_SEC      (24 * 3600)

#define FT_OPS_BACK_NEW_DIR         "new_creat"
#define FT_OPS_BACK_SUB_DIR         "subtype"
#define FT_OPS_BACK_SUB_DIR_LEN     7

#define FT_OPS_NEW_INFO_SET(info)       ((info)->res = NULL)
#define FT_OPS_NEW_INFO_IS(info)        ((info)->res == NULL)

struct ft_ops_cfg;
struct ft_acq_thread;

/* 删除指定文件夹 */
void ft_dir_remove(const char *dir);

/* 完成命令执行 */
int ft_sys_execl(int wait_cld, const char *pro_name, ...);

/* 依据配置重置运维信息 */
int ft_ops_reset(struct ft_ops_cfg *cfg);

/* 备份或删除超时异常文件，返回值：0-未处理，1-备份或删除成功*/
int ft_abn_file_or_dir_ops(struct ft_file_info *info, time_t cur_sec);

/* 备份或删除正常上报文件，返回值：0-未处理，1-备份或删除成功*/
int ft_nor_file_or_dir_ops(struct ft_file_info *info, time_t cur_sec);

/* 定时器到期，删除备份超时的文件内容 */
void ft_ops_backup_del(struct ft_acq_thread *acq);


#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FT_OPS_H__ */
