#ifndef __FT_MPROC_H__
#define __FT_MPROC_H__

#include "ft.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define FT_MPROC_SHM_FILE   "/var/run/.hh_ft_shm"

/* 主进程共享内存初始化 */
void *ft_mproc_master_init(void);
uint8_t ft_mproc_master_msg_start(struct ft_mproc_msg *msg);
void ft_mproc_master_msg_stop(struct ft_mproc_msg *msg);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FT_MPROC_H__ */
