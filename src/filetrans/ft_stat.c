#include <string.h>
#include <time.h>
#include "ft.h"

/* 全局统计管理器 */
static struct ft_stat_manager g_stat_manager = {0};

struct ft_stat_manager *ft_stat_manager_get(void)
{
    return &g_stat_manager;
}

void ft_stat_clear_all(void)
{
    memset(&g_stat_manager, 0, sizeof(g_stat_manager));
}

static int ft_stat_validate_params(enum ft_stat_metric_type type, uint8_t thread_idx)
{
    if (type >= FT_STAT_METRIC_MAX) {
        return -1;
    }
    if (thread_idx >= FT_THREAD_MAX) {
        return -1;
    }
    return 0;
}

static struct ft_performance_stat *ft_stat_get_perf_stat(enum ft_stat_metric_type type, uint8_t thread_idx)
{
    struct ft_stat_manager *manager = &g_stat_manager;

    switch (type) {
    case FT_STAT_METRIC_UPLOAD:
        return &manager->upload_stats[thread_idx];
    case FT_STAT_METRIC_COMPRESS:
        return &manager->compress_stats[thread_idx].perf_stat;
    default:
        return NULL;
    }
}

static void ft_stat_perf_init(struct ft_performance_stat *perf_stat, enum ft_stat_metric_type type)
{
    memset(perf_stat->window, 0, sizeof(perf_stat->window));
    perf_stat->current_idx = 0;
    perf_stat->last_update = 0;
    perf_stat->type = type;
}

static int ft_stat_perf_update(struct ft_performance_stat *perf_stat,
                               size_t bytes, uint64_t files, double ratio)
{
    time_t current_time = time(NULL);
    time_t time_slice = current_time / FT_STAT_TIME_SLICE * FT_STAT_TIME_SLICE;

    /* 检查是否需要移动到新的时间片 */
    if (perf_stat->last_update == 0 ||
        time_slice != perf_stat->window[perf_stat->current_idx].timestamp) {

        /* 移动到下一个环形缓冲区位置 */
        perf_stat->current_idx++;
        if (perf_stat->current_idx >= FT_STAT_WINDOW_SIZE) {
            perf_stat->current_idx = 0;
        }

        /* 初始化新的时间片 */
        perf_stat->window[perf_stat->current_idx].timestamp = time_slice;
        perf_stat->window[perf_stat->current_idx].bytes = bytes;
        perf_stat->window[perf_stat->current_idx].files = files;
        perf_stat->window[perf_stat->current_idx].ratio = ratio;
    } else {
        /* 在当前时间片内累加数据 */
        struct ft_stat_slice *current_slice = &perf_stat->window[perf_stat->current_idx];
        current_slice->bytes += bytes;
        current_slice->files += files;

        /* 计算加权平均比率 */
        if (current_slice->files > 0) {
            current_slice->ratio = ((current_slice->ratio * (current_slice->files - files)) +
                                   (ratio * files)) / current_slice->files;
        }
    }

    perf_stat->last_update = current_time;
    return 0;
}

static int ft_stat_perf_get(struct ft_performance_stat *perf_stat,
                            double *rate, double *fps, double *avg_ratio)
{
    time_t current_time = time(NULL);
    time_t oldest_valid_time = current_time - FT_STAT_WINDOW_SIZE * FT_STAT_TIME_SLICE;

    size_t total_bytes = 0;
    uint64_t total_files = 0;
    double total_ratio = 0.0;
    int valid_slices = 0;
    int i;

    /* 遍历环形缓冲区，收集有效数据 */
    for (i = 0; i < FT_STAT_WINDOW_SIZE; i++) {
        volatile time_t timestamp = perf_stat->window[i].timestamp;
        if (timestamp > 0 && timestamp >= oldest_valid_time) {
            total_bytes += perf_stat->window[i].bytes;
            total_files += perf_stat->window[i].files;
            total_ratio += perf_stat->window[i].ratio * perf_stat->window[i].files;
            valid_slices++;
        }
    }

    /* 如果没有有效数据，返回0 */
    if (valid_slices == 0 || total_bytes == 0) {
        *rate = *fps = *avg_ratio = 0.0;
        return 0;
    }

    /* 计算实时速率：基于窗口大小 */
    double time_window = FT_STAT_WINDOW_SIZE * FT_STAT_TIME_SLICE;
    *rate = (double)total_bytes / time_window;
    *fps = (double)total_files / time_window;
    *avg_ratio = (total_files > 0) ? (total_ratio / total_files) : 0.0;

    return 0;
}

int ft_stat_init(enum ft_stat_metric_type type, uint8_t thread_idx)
{
    if (ft_stat_validate_params(type, thread_idx) != 0) {
        return -1;
    }

    struct ft_performance_stat *perf_stat = ft_stat_get_perf_stat(type, thread_idx);
    if (!perf_stat) {
        return -1;
    }

    ft_stat_perf_init(perf_stat, type);

    /* 如果是压缩统计，还需要初始化累计数据 */
    if (type == FT_STAT_METRIC_COMPRESS) {
        struct ft_compress_stat *comp_stat = &g_stat_manager.compress_stats[thread_idx];
        comp_stat->total_input_bytes = 0;
        comp_stat->total_output_bytes = 0;
        comp_stat->total_files = 0;
    }

    return 0;
}

int ft_stat_update(enum ft_stat_metric_type type, uint8_t thread_idx,
                   size_t bytes, uint64_t files, double ratio)
{
    if (ft_stat_validate_params(type, thread_idx) != 0) {
        return -1;
    }

    struct ft_performance_stat *perf_stat = ft_stat_get_perf_stat(type, thread_idx);
    if (!perf_stat) {
        return -1;
    }

    return ft_stat_perf_update(perf_stat, bytes, files, ratio);
}

int ft_stat_get(enum ft_stat_metric_type type, uint8_t thread_idx,
                double *rate, double *fps, double *avg_ratio)
{
    if (ft_stat_validate_params(type, thread_idx) != 0 ||
        !rate || !fps || !avg_ratio) {
        return -1;
    }

    struct ft_performance_stat *perf_stat = ft_stat_get_perf_stat(type, thread_idx);
    if (!perf_stat) {
        return -1;
    }

    return ft_stat_perf_get(perf_stat, rate, fps, avg_ratio);
}

void ft_up_stat_init(uint8_t up_idx)
{
    ft_stat_init(FT_STAT_METRIC_UPLOAD, up_idx);
}

void ft_up_stat_update(uint8_t up_idx, size_t bytes, uint64_t files)
{
    /* 比率对上传无意义，设为0.0 */
    ft_stat_update(FT_STAT_METRIC_UPLOAD, up_idx, bytes, files, 0.0);
}

void ft_up_stat_get(double *KBps, double *fps, uint8_t up_idx)
{
    double rate_Bps, avg_ratio;

    int ret = ft_stat_get(FT_STAT_METRIC_UPLOAD, up_idx, &rate_Bps, fps, &avg_ratio);
    if (ret != 0) {
        *KBps = *fps = 0.0;
        return;
    }

    /* 转换为KB/s */
    *KBps = rate_Bps / 1024.0;
}

int ft_comp_stat_init(uint8_t comp_idx)
{
    return ft_stat_init(FT_STAT_METRIC_COMPRESS, comp_idx);
}

int ft_comp_stat_update(uint8_t comp_idx, size_t input_bytes,
                        size_t output_bytes, uint64_t files)
{
    if (comp_idx >= FT_THREAD_MAX) {
        return -1;
    }

    struct ft_compress_stat *comp_stat = &g_stat_manager.compress_stats[comp_idx];

    /* 更新累计统计 */
    comp_stat->total_input_bytes += input_bytes;
    comp_stat->total_output_bytes += output_bytes;
    comp_stat->total_files += files;

    /* 计算压缩比率 */
    double compression_ratio = (output_bytes > 0) ?
                              ((double)input_bytes / output_bytes) : 0.0;

    /* 更新滑动窗口统计（基于输入字节数） */
    return ft_stat_update(FT_STAT_METRIC_COMPRESS, comp_idx,
                         input_bytes, files, compression_ratio);
}

int ft_comp_stat_get(uint8_t comp_idx, double *compress_rate_KBps,
                     double *fps, double *compression_ratio)
{
    if (comp_idx >= FT_THREAD_MAX ||
        !compress_rate_KBps || !fps || !compression_ratio) {
        return -1;
    }

    double rate_Bps, avg_ratio;
    int ret = ft_stat_get(FT_STAT_METRIC_COMPRESS, comp_idx,
                         &rate_Bps, fps, &avg_ratio);
    if (ret != 0) {
        return ret;
    }

    /* 转换为KB/s */
    *compress_rate_KBps = rate_Bps / 1024.0;
    *compression_ratio = avg_ratio;

    return 0;
}


