#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "ft.h"
#include "ft_stat.h"

/* 定义成功和失败的返回值 */
#define FT_OK    0
#define FT_FAIL -1

/* 添加线程索引越界检查函数 */
static inline int ft_stat_check_thread_idx(uint8_t thread_idx, enum ft_stat_metric_type type)
{
    /* 检查统计类型是否有效 */
    if (type >= FT_STAT_METRIC_MAX) {
        FT_ERROR("Invalid statistic metric type: %d\n", type);
        return FT_FAIL;
    }

    /* 检查线程索引是否越界 */
    if (thread_idx >= FT_THREAD_MAX) {
        FT_ERROR("Thread index out of bounds: %d\n", thread_idx);
        return FT_FAIL;
    }

    /* 检查压缩线程索引是否在有效范围内 */
    if (type == FT_STAT_METRIC_COMPRESS && thread_idx >= g_ft_ctx.cfg.comp_cnt) {
        FT_ERROR("Compression thread index out of bounds: %d, max: %d\n", 
                thread_idx, g_ft_ctx.cfg.comp_cnt);
        return FT_FAIL;
    }

    /* 检查上传线程索引是否在有效范围内 */
    if (type == FT_STAT_METRIC_UPLOAD && thread_idx >= g_ft_ctx.cfg.up_cnt) {
        FT_ERROR("Upload thread index out of bounds: %d, max: %d\n", 
                thread_idx, g_ft_ctx.cfg.up_cnt);
        return FT_FAIL;
    }

    return FT_OK;
}

/* 修改现有的基于索引的统计初始化函数，添加错误处理 */
int ft_comp_stat_init_by_idx(uint8_t comp_idx)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(comp_idx, FT_STAT_METRIC_COMPRESS) != FT_OK) {
        return FT_FAIL;
    }

    /* 获取对应的压缩线程统计结构 */
    struct ft_compress_stat *comp_stat = &g_ft_ctx.comp[comp_idx].stat;
    
    /* 调用直接初始化函数 */
    return ft_comp_stat_init(comp_stat);
}

/* 修改现有的基于索引的统计更新函数，添加错误处理 */
int ft_comp_stat_update_by_idx(uint8_t comp_idx, size_t input_bytes, 
                              size_t output_bytes, uint64_t files)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(comp_idx, FT_STAT_METRIC_COMPRESS) != FT_OK) {
        return FT_FAIL;
    }

    /* 检查输入参数有效性 */
    if (input_bytes == 0 && files > 0) {
        FT_ERROR("Invalid input bytes: %zu for files: %lu\n", input_bytes, files);
        /* 不返回错误，继续处理 */
    }

    /* 获取对应的压缩线程统计结构 */
    struct ft_compress_stat *comp_stat = &g_ft_ctx.comp[comp_idx].stat;
    
    /* 调用直接更新函数 */
    return ft_comp_stat_update(comp_stat, input_bytes, output_bytes, files);
}

/* 修改现有的基于索引的统计查询函数，添加错误处理 */
int ft_comp_stat_get_by_idx(uint8_t comp_idx, double *compress_rate_KBps, 
                           double *fps, double *compression_ratio)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(comp_idx, FT_STAT_METRIC_COMPRESS) != FT_OK) {
        return FT_FAIL;
    }

    /* 检查输出参数有效性 */
    if (!compress_rate_KBps && !fps && !compression_ratio) {
        FT_ERROR("All output parameters are NULL\n");
        return FT_FAIL;
    }

    /* 获取对应的压缩线程统计结构 */
    struct ft_compress_stat *comp_stat = &g_ft_ctx.comp[comp_idx].stat;
    
    /* 调用直接查询函数 */
    return ft_comp_stat_get(comp_stat, compress_rate_KBps, fps, compression_ratio);
}/* 通
用统计初始化（基于线程索引） */
int ft_stat_init_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(thread_idx, type) != FT_OK) {
        return FT_FAIL;
    }

    /* 根据统计类型获取对应的统计结构 */
    struct ft_performance_stat *stat = NULL;
    
    if (type == FT_STAT_METRIC_UPLOAD) {
        stat = &g_ft_ctx.up[thread_idx].stat.perf_stat;
    } else if (type == FT_STAT_METRIC_COMPRESS) {
        stat = &g_ft_ctx.comp[thread_idx].stat.perf_stat;
    } else {
        FT_ERROR("Unsupported statistic metric type: %d\n", type);
        return FT_FAIL;
    }
    
    /* 调用直接初始化函数 */
    return ft_stat_init(stat, type);
}

/* 通用统计更新（基于线程索引） */
int ft_stat_update_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx, 
                         size_t bytes, uint64_t files, double ratio)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(thread_idx, type) != FT_OK) {
        return FT_FAIL;
    }

    /* 根据统计类型获取对应的统计结构 */
    struct ft_performance_stat *stat = NULL;
    
    if (type == FT_STAT_METRIC_UPLOAD) {
        stat = &g_ft_ctx.up[thread_idx].stat.perf_stat;
    } else if (type == FT_STAT_METRIC_COMPRESS) {
        stat = &g_ft_ctx.comp[thread_idx].stat.perf_stat;
    } else {
        FT_ERROR("Unsupported statistic metric type: %d\n", type);
        return FT_FAIL;
    }
    
    /* 调用直接更新函数 */
    return ft_stat_update(stat, bytes, files, ratio);
}

/* 通用统计查询（基于线程索引） */
int ft_stat_get_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx,
                      double *rate_KBps, double *fps, double *avg_ratio)
{
    /* 检查线程索引是否有效 */
    if (ft_stat_check_thread_idx(thread_idx, type) != FT_OK) {
        return FT_FAIL;
    }

    /* 检查输出参数有效性 */
    if (!rate_KBps && !fps && !avg_ratio) {
        FT_ERROR("All output parameters are NULL\n");
        return FT_FAIL;
    }

    /* 根据统计类型获取对应的统计结构 */
    struct ft_performance_stat *stat = NULL;
    
    if (type == FT_STAT_METRIC_UPLOAD) {
        stat = &g_ft_ctx.up[thread_idx].stat.perf_stat;
    } else if (type == FT_STAT_METRIC_COMPRESS) {
        stat = &g_ft_ctx.comp[thread_idx].stat.perf_stat;
    } else {
        FT_ERROR("Unsupported statistic metric type: %d\n", type);
        return FT_FAIL;
    }
    
    /* 调用直接查询函数 */
    return ft_stat_get(stat, rate_KBps, fps, avg_ratio);
}/* 通
用统计初始化 */
int ft_stat_init(struct ft_performance_stat *stat, enum ft_stat_metric_type type)
{
    if (!stat) {
        FT_ERROR("Invalid statistic structure pointer\n");
        return FT_FAIL;
    }

    /* 初始化统计结构 */
    memset(stat, 0, sizeof(struct ft_performance_stat));
    stat->type = type;
    stat->current_idx = 0;
    stat->last_update = time(NULL);

    return FT_OK;
}

/* 通用统计更新 */
int ft_stat_update(struct ft_performance_stat *stat, size_t bytes, 
                  uint64_t files, double ratio)
{
    if (!stat) {
        FT_ERROR("Invalid statistic structure pointer\n");
        return FT_FAIL;
    }

    time_t current_time = time(NULL);
    
    /* 检查是否需要切换到新的时间片 */
    if (current_time - stat->last_update >= FT_STAT_TIME_SLICE) {
        /* 更新索引到下一个时间片 */
        stat->current_idx = (stat->current_idx + 1) % FT_STAT_WINDOW_SIZE;
        
        /* 清空新时间片数据 */
        stat->window[stat->current_idx].timestamp = current_time;
        stat->window[stat->current_idx].bytes = 0;
        stat->window[stat->current_idx].files = 0;
        stat->window[stat->current_idx].ratio = 0.0;
        
        /* 更新最后更新时间 */
        stat->last_update = current_time;
    }

    /* 更新当前时间片的统计数据 */
    stat->window[stat->current_idx].bytes += bytes;
    stat->window[stat->current_idx].files += files;
    
    /* 更新比率（如果有效） */
    if (ratio > 0) {
        /* 使用加权平均计算比率 */
        double old_bytes = stat->window[stat->current_idx].bytes - bytes;
        double old_ratio = stat->window[stat->current_idx].ratio;
        
        if (old_bytes > 0) {
            /* 加权平均：(old_bytes * old_ratio + bytes * ratio) / total_bytes */
            stat->window[stat->current_idx].ratio = 
                (old_bytes * old_ratio + bytes * ratio) / 
                stat->window[stat->current_idx].bytes;
        } else {
            stat->window[stat->current_idx].ratio = ratio;
        }
    }

    return FT_OK;
}

/* 通用统计查询 */
int ft_stat_get(struct ft_performance_stat *stat, double *rate_KBps, 
               double *fps, double *avg_ratio)
{
    if (!stat) {
        FT_ERROR("Invalid statistic structure pointer\n");
        return FT_FAIL;
    }

    time_t current_time = time(NULL);
    time_t oldest_valid_time = current_time - FT_STAT_WINDOW_SIZE * FT_STAT_TIME_SLICE;

    size_t total_bytes = 0;
    uint64_t total_files = 0;
    double weighted_ratio_sum = 0.0;
    int valid_slices = 0;
    int i;

    /* 遍历环形缓冲区，收集有效数据 */
    for (i = 0; i < FT_STAT_WINDOW_SIZE; i++) {
        volatile time_t timestamp = stat->window[i].timestamp;
        if (timestamp > 0 && timestamp >= oldest_valid_time) {
            total_bytes += stat->window[i].bytes;
            total_files += stat->window[i].files;
            
            /* 计算加权比率总和 */
            if (stat->window[i].bytes > 0) {
                weighted_ratio_sum += stat->window[i].ratio * stat->window[i].bytes;
            }
            
            valid_slices++;
        }
    }

    /* 如果没有有效数据，返回0 */
    if (valid_slices == 0 || total_bytes == 0) {
        if (rate_KBps) *rate_KBps = 0;
        if (fps) *fps = 0;
        if (avg_ratio) *avg_ratio = 0;
        return FT_OK;
    }

    /* 计算实时速率：基于窗口大小 */
    double time_window = FT_STAT_WINDOW_SIZE * FT_STAT_TIME_SLICE;
    
    if (rate_KBps) {
        *rate_KBps = (double)total_bytes / 1024 / time_window;
    }
    
    if (fps) {
        *fps = (double)total_files / time_window;
    }
    
    /* 计算加权平均比率 */
    if (avg_ratio) {
        *avg_ratio = (total_bytes > 0) ? weighted_ratio_sum / total_bytes : 0;
    }

    return FT_OK;
}

/* 压缩统计初始化 */
int ft_comp_stat_init(struct ft_compress_stat *comp_stat)
{
    if (!comp_stat) {
        FT_ERROR("Invalid compression statistic structure pointer\n");
        return FT_FAIL;
    }

    /* 初始化通用统计结构 */
    int ret = ft_stat_init(&comp_stat->perf_stat, FT_STAT_METRIC_COMPRESS);
    if (ret != FT_OK) {
        return ret;
    }

    /* 初始化压缩特有字段 */
    comp_stat->total_input_bytes = 0;
    comp_stat->total_output_bytes = 0;
    comp_stat->total_files = 0;

    return FT_OK;
}

/* 压缩统计更新 */
int ft_comp_stat_update(struct ft_compress_stat *comp_stat, size_t input_bytes, 
                       size_t output_bytes, uint64_t files)
{
    if (!comp_stat) {
        FT_ERROR("Invalid compression statistic structure pointer\n");
        return FT_FAIL;
    }

    /* 更新累计统计 */
    comp_stat->total_input_bytes += input_bytes;
    comp_stat->total_output_bytes += output_bytes;
    comp_stat->total_files += files;

    /* 计算压缩比率：输入大小/输出大小 */
    double ratio = 0.0;
    if (output_bytes > 0) {
        ratio = (double)input_bytes / output_bytes;
    }

    /* 更新通用统计（使用输入字节数作为处理量） */
    return ft_stat_update(&comp_stat->perf_stat, input_bytes, files, ratio);
}

/* 压缩统计查询 */
int ft_comp_stat_get(struct ft_compress_stat *comp_stat, double *compress_rate_KBps, 
                    double *fps, double *compression_ratio)
{
    if (!comp_stat) {
        FT_ERROR("Invalid compression statistic structure pointer\n");
        return FT_FAIL;
    }

    /* 查询通用统计 */
    return ft_stat_get(&comp_stat->perf_stat, compress_rate_KBps, fps, compression_ratio);
}