#include <sys/prctl.h>
#include <time.h>
#include "ft.h"
#include "hmemory.h"

struct ft_ctx g_ft_ctx;

int ft_thread_acquire_creat(struct ft_ctx *ctx);
int ft_thread_upload_creat(struct ft_ctx *ctx);
int ft_thread_compress_creat(struct ft_ctx *ctx);

void ft_tsf_cfg_update(struct ft_transfer *tsf, struct ft_tsf_cfg *new_cfg)
{
    pthread_rwlock_wrlock(&tsf->rwlock);
    memcpy(&tsf->cfg, new_cfg, sizeof(struct ft_tsf_cfg));
    pthread_rwlock_unlock(&tsf->rwlock);
}

int ft_outfile_up_name_creat(struct ft_file_info *info, char *name, size_t len)
{
    if (!info->is_comp) {
        size_t file_name_len = FT_FILE_NAME_LEN(&info->fc);
        if (likely(len > file_name_len)) {
            memcpy(name, FT_FILE_NAME(&info->fc), file_name_len);
            name[file_name_len] = 0;
            return FT_OK;
        }
    } else {
        if (COMP_OK == compress_out_file_name_init(info->icc_hdr.icc_list_hdr,
                    &info->occ, name, len, 0)) {
            return FT_OK;
        }
    }
    return FT_FAIL;
}

int ft_outfile_local_name_creat(struct ft_file_info *info, char *name, size_t len)
{
    if (!info->is_comp) {
        if (likely(len > info->fc.path_len)) {
            memcpy(name, info->fc.path, info->fc.path_len);
            name[info->fc.path_len] = 0;
        }
    } else {
        if (COMP_OK == compress_out_file_name_init(info->icc_hdr.icc_list_hdr,
                    &info->occ, name, len, 1)) {
            return FT_OK;
        }
    }

    return FT_FAIL;
}

static inline char *ft_get_fi_comp_path(struct ft_transfer *tsf)
{
    return tsf->cfg.comp_path[0] ? tsf->cfg.comp_path : NULL;
}

static inline char *ft_get_fi_comp_pswd(struct ft_transfer *tsf)
{
    return tsf->cfg.comp_pswd[0] ? tsf->cfg.comp_pswd : NULL;
}

uint8_t ft_get_fi_comp_type(uint8_t log_type)
{
    struct ft_transfer *tsf = g_ft_ctx.tsf[log_type];
    return tsf->cfg.compress_type;
}

char *ft_get_info_path(struct ft_file_info *info)
{
    return info->is_comp ? info->icc_hdr.icc_list_hdr->ipi.path : info->fc.path;
}

void ft_signal_dispose(int sig)
{
    FT_DEBUG("sig = %d\n", sig);

    uint32_t i;
    g_ft_ctx.acq.sig_flag = 1;
    for (i = 0; i < g_ft_ctx.cfg.up_cnt; i++) {
        g_ft_ctx.up[i].sig_flag = 1;
    }
}

void *ft_abnormal_info_get(void)
{
    return &g_ft_ctx.acq.abn.info;
}

uint32_t ft_transfer_get(struct ft_transfer **tsf, uint32_t max, int type)
{
    struct ft_ctx *ctx = &g_ft_ctx;

    if (type == -1) {
        uint16_t i;
        if (unlikely(ctx->tsf_cnt >= max))
            return 0;
        for (i = 0; i < ctx->tsf_cnt; i++) {
            tsf[i] = ctx->tsf[ctx->tsf_idx[i]];
        }
        return ctx->tsf_cnt;
    } else {
        if (unlikely(type >= FT_TRANSFER_TYPE_MAX))
            return 0;
        if (!ctx->tsf[type])
            return 0;
        *tsf = ctx->tsf[type];
        return 1;
    }
}

void *ft_cfg_get(void)
{
    return &g_ft_ctx.cfg;
}

void ft_status_set(uint8_t status)
{
    g_ft_ctx.status = status;
}

uint8_t ft_status_get(void)
{
    if (g_ft_ctx.status)
        return FT_FILETRANS_START;
    return FT_FILETRANS_STOP;
}

/*
 * @brief 创建目录检测函数.
 */
static int __ft_mkdir(const char *dir, int flag)
{
    int ret;
    int i, len;
    char str[FT_MAX_LEN_512];

    len = strlen(dir);
    if (len >= sizeof(str))
        return FT_FAIL;
    memcpy(str, dir, len);
    str[len] = 0;

    for (i = 0; i < len; i++) {
        if ('/' == str[i] && 0 != i) {
            str[i] = '\0';
            if (access(str, F_OK)) {
                ret = mkdir(str, FT_DIR_PERMISSION);
                if (0 != ret) {
                    return FT_FAIL;
                }
            }
            str[i] = '/';
        }
    }

    if (flag) {
        if (len > 0 && 0 != access(str, F_OK)) {
            ret = mkdir(str, FT_DIR_PERMISSION);
            if (0 != ret) {
                return FT_FAIL;
            }
        }
    }

    return FT_OK;
}

/*
 * @brief 创建目录函数.
 */
int ft_mkdir(const char *dir)
{
    int ret;

    if (0 != access(dir, F_OK)) {
        ret = mkdir(dir, FT_DIR_PERMISSION);
        if (0 != ret && FT_OK != __ft_mkdir(dir, 1)) {
            FT_ERROR("mkdir %s err\n", dir);
            return FT_FAIL;
        }
    }

    return FT_OK;
}

static inline int ft_bind_cpu(int core_id)
{
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(core_id, &mask);

    if (pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask) < 0) {
        FT_ERROR("Bind core_id %d failed\n", core_id);
        return FT_FAIL;
    }

    return FT_OK;
}

/* 设置亲和性/线程名 */
int ft_thread_priv_attr_set(uint16_t bind_core, char *name)
{
    prctl(PR_SET_NAME, name);

    if (bind_core)
        return ft_bind_cpu(bind_core);

    return FT_OK;
}

/* 设置资源源线程名/目的线程名 */
void ft_thread_res_name_set(struct ft_duty_res *res, char *name, int is_src)
{
    if (is_src) {
        snprintf(res->src_name, sizeof(res->src_name), "%s", name);
    } else {
        snprintf(res->dst_name, sizeof(res->dst_name), "%s", name);
    }
}

void *ft_file_mem_mmap(int fd, size_t sz)
{
    int rw_flag = PROT_READ;
    int map_flag = (MAP_PRIVATE | MAP_POPULATE);
    /* 预填充映射的page，避免压缩时的Pagefault(MAP_POPULATE) */
    void *mem = mmap(NULL, FT_PGUP(sz, FT_SYS_PGSZ), rw_flag, map_flag, fd, 0);
    return (mem == MAP_FAILED) ? NULL : mem;
}

void ft_file_mem_munmap(void *buf, size_t sz)
{
    if (unlikely(!buf))
        return;
    if (0 != munmap(buf, FT_PGUP(sz, FT_SYS_PGSZ)))
        FT_ERROR("munmap err(%s)", strerror(errno));
}

void *ft_comp_icc_obj_get(struct ft_duty_res *res)
{
    int ret;
    struct comp_input_centre *icc;

    ret = rte_mempool_get(res->icc_pool, (void**)&icc);
    if (unlikely(ret != 0)) {
        res->stat.icc_pool_get_err++;
        return NULL;
    }

    res->stat.icc_pool_get_ok++;
    compress_icc_init(icc);
    return icc;
}

void ft_file_centre_init(struct ft_file_centre *fc)
{
    fc->buf = NULL;
}

static inline void ft_file_comp_meta_init(struct ft_file_info *info, uint8_t input_type)
{
    struct ft_transfer *tsf = g_ft_ctx.tsf[info->type];

    /* icc初始化 */
    compress_icc_hdr_init(&info->icc_hdr, input_type);

    pthread_rwlock_rdlock(&tsf->rwlock);
    /* occ初始化 */
    compress_occ_init(&info->occ,
            ft_get_fi_comp_path(tsf),
            ft_get_fi_comp_pswd(tsf),
            ft_get_fi_comp_type(info->type));
    pthread_rwlock_unlock(&tsf->rwlock);
}

void *ft_file_info_obj_get(struct ft_duty_res *res, uint32_t log_type, uint8_t input_type)
{
    /* type类型不能超出最大限制 */
    if (unlikely(log_type >= FT_TRANSFER_TYPE_MAX))
        return NULL;

    int ret;
    struct ft_file_info *info;

    ret = rte_mempool_get(res->pool, (void**)&info);
    if (unlikely(ret != 0)) {
        res->stat.pool_get_err++;
        return NULL;
    }

    res->stat.pool_get_ok++;
    info->type = log_type;
    info->res = res;

    uint8_t comp_type = ft_get_fi_comp_type(log_type);
    if (comp_type == FT_COMPRESS_NONE) {
        info->is_comp = 0;
        ft_file_centre_init(&info->fc);
    } else {
        info->is_comp = 1;
        ft_file_comp_meta_init(info, input_type);
    }

    info->attr.line = info->attr.sub_type = info->attr.platform = 0;
    info->attr.usr_priv = NULL;
    return info;
}

void ft_comp_icc_obj_put(struct comp_input_centre *icc, struct ft_duty_res *res)
{
    compress_icc_deinit(icc);
    if (icc->buf)
        ft_file_mem_munmap(icc->buf, icc->filestat.st_size);
    if (res) {
        rte_mempool_put(res->icc_pool, icc);
        res->stat.icc_pool_put++;
    }
}

void ft_file_info_obj_put(struct ft_file_info *info)
{
    if (info->is_comp) {
        struct comp_input_centre *pair_icc, *pad_icc;
        /* 回收文件列表 */
        pair_icc = info->icc_hdr.icc_list_hdr;
        while (pair_icc) {
            pad_icc = pair_icc;
            pair_icc = pair_icc->next;
            ft_comp_icc_obj_put(pad_icc, info->icc_res);
        }
        compress_occ_deinit(&info->occ);
    } else {
        /* 回收文件主结构 */
        if (info->fc.buf)
            ft_file_mem_munmap(info->fc.buf, info->fc.filestat.st_size);
    }
    struct ft_duty_res *res = info->res;
    ft_file_info_priv_mem_put(info->attr.usr_priv);
    rte_mempool_put(res->pool, info);
    res->stat.pool_put++;
}

int ft_abnormal_info_put(struct ft_file_info *info)
{
    struct ft_duty_res *res = &g_ft_ctx.acq.abn.res;
    struct ft_duty_stat *stat = &res->stat;

    if (0 != rte_ring_enqueue(res->ring, info)) {
        ft_file_info_obj_put(info);
        stat->en_err++;
        return FT_FAIL;
    }

    stat->en_ok++;
    return FT_OK;
}

int ft_res_entry_put(struct ft_duty_res *res, void *entry, uint8_t en_abn)
{
    struct ft_file_info *info = entry;
    struct ft_duty_stat *stat = &res->stat;

    /* 本线程资源入队失败,由en_abn确认是否需要入队给异常处理队列 */
    if (0 != rte_ring_enqueue(res->ring, info)) {
        stat->en_err++;
        if (!en_abn)
            return FT_FAIL;
        return ft_abnormal_info_put(info);
    }

    stat->en_ok++;
    return FT_OK;
}

int ft_res_entry_get(struct ft_duty_res *res, void **entry)
{
    struct ft_duty_stat *stat = &res->stat;
    if (0 != rte_ring_dequeue(res->ring, entry)) {
        return FT_FAIL;
    }
    stat->de_ok++;
    return FT_OK;
}

int ft_cfg_init(struct ft_cfg *cfg, const struct ft_cfg *param)
{
    memcpy(cfg, param, sizeof(*param));

    /* 创建filetrans主目录 */
    if (!cfg->path[0] || FT_OK != ft_mkdir(cfg->path))
        return FT_FAIL;

    if (cfg->comp_level < FT_COMPRESS_LEVEL_FASTEST ||
            cfg->comp_level > FT_COMPRESS_LEVEL_NONE) {
        /* 默认压缩级别 */
        cfg->comp_level = FT_COMPRESS_LEVEL_DEFAULT;
    }

    return FT_OK;
}

int ft_early_init(struct ft_ctx *ctx, const struct ft_cfg *param)
{
    int ret;

    /* note: 初始化过程中不可对ctx做清零操作，否则会清空已注册的transfer */

    /* 配置初始化 */
    ret = ft_cfg_init(&ctx->cfg, param);
    if (ret != FT_OK) {
        FT_ERROR("filetrans cfg error\n");
        return FT_FAIL;
    }

    /* 获取pgsz,开启服务 */
    ctx->pgsz = getpagesize();
    ctx->status = FT_FILETRANS_START;

    return FT_OK;
}

static int ft_thread_creat(struct ft_ctx *ctx)
{
    int ret;

    /* 文件信息获取线程创建 */
    ret = ft_thread_acquire_creat(ctx);
    FT_ERR_CHK_RET(ret);

    /* 文件压缩线程创建 */
    ret = ft_thread_compress_creat(ctx);
    FT_ERR_CHK_RET(ret);

    /* 文件上报线程创建 */
    ret = ft_thread_upload_creat(ctx);
    FT_ERR_CHK_RET(ret);

    return FT_OK;
}

void ft_tsf_plat_srv_init(struct ft_up_srv *srv, uint8_t plat)
{
    srv->plat = plat;
    if (FT_UPLOAD_SFTP == srv->up_mode) {
        if (FT_IP_VER4 == srv->ipver) {
            snprintf(srv->seg_url, sizeof(srv->seg_url), "sftp://%s:%u",
                    srv->host, srv->port);
        } else {
            snprintf(srv->seg_url, sizeof(srv->seg_url), "sftp://[%s]:%u",
                    srv->host, srv->port);
        }
    } else if (FT_UPLOAD_FTP == srv->up_mode) {
        if (FT_IP_VER4 == srv->ipver) {
            snprintf(srv->seg_url, sizeof(srv->seg_url), "ftp://%s:%u",
                    srv->host, srv->port);
        } else {
            snprintf(srv->seg_url, sizeof(srv->seg_url), "ftp://[%s]:%u",
                    srv->host, srv->port);
        }
    }
}

static int ft_transfer_platform_init(struct ft_tsf_cfg *cfg)
{
#define FT_OPS_SELF_PLAT    FT_OPS_BACK_NEW_DIR
    uint32_t i, j;
    struct ft_up_platform *plat;
    struct ft_up_srv *srv;

    for (i = 0; i < cfg->plat_cnt; i++) {
        plat = &cfg->platform[cfg->plat_idx[i]];
        plat->type = cfg->plat_idx[i];
        /* 1. 平台名必需
         * 2. 内部处理自建文件的备份目录，作为内部备份的自有平台，不得与其同名
         * 3. 不得与subtype同名
         * */
        if (!plat->name[0] || !strncmp(plat->name, FT_OPS_SELF_PLAT,
                    strlen(FT_OPS_SELF_PLAT)) || !strncmp(plat->name,
                    FT_OPS_BACK_SUB_DIR, FT_OPS_BACK_SUB_DIR_LEN)) {
            return FT_FAIL;
        }
        for (j = 0; j < plat->srv_cnt; j++) {
            srv = &plat->upsrv[j];
            ft_tsf_plat_srv_init(srv, plat->type);
        }
    }

    return FT_OK;
}

static inline int ft_transfer_check(struct ft_transfer *tsf)
{
    /* 平台数及平台索引合法 */
    if (!tsf->cfg.plat_cnt || tsf->cfg.plat_cnt > FT_UP_PLATFORM_CNT_MAX) {
        return FT_FAIL;
    } else {
        int i;
        for (i = 0; i < tsf->cfg.plat_cnt; i++) {
            if (tsf->cfg.plat_idx[i] >= FT_UP_PLATFORM_CNT_MAX)
                return FT_FAIL;
        }
    }

    return FT_OK;
}

static inline int ft_transfer_base_check(struct ft_transfer *tsf)
{
    struct ft_ctx *ctx = &g_ft_ctx;

    if (unlikely(tsf->type >= FT_TRANSFER_TYPE_MAX))
        return FT_FAIL;
    /* 不可被多次注册 */
    if (NULL != ctx->tsf[tsf->type]) {
        FT_ERROR("tsf %s is exist\n", tsf->name);
        return FT_FAIL;
    }
    /* upload hook点必需 */
    if (!tsf->hook[FT_FILE_UPLOAD])
        return FT_FAIL;

    return FT_OK;
}

int ft_transfer_reg(struct ft_transfer *tsf)
{
    struct ft_ctx *ctx = &g_ft_ctx;

    /* transfer基础检查 */
    if (FT_OK != ft_transfer_base_check(tsf)) {
        FT_ERROR("tsf %s base check err\n", tsf->name);
        return FT_FAIL;
    }

    /* tsf初始化 */
    if (!tsf->init || FT_OK != tsf->init(tsf)) {
        FT_ERROR("tsf %s init err\n", tsf->name);
        return FT_FAIL;
    }

    /* transfer检查 */
    if (FT_OK != ft_transfer_check(tsf)) {
        FT_ERROR("tsf %s check err\n", tsf->name);
        return FT_FAIL;
    }

    /* 待上报平台服务器信息初始化 */
    if (FT_OK != ft_transfer_platform_init(&tsf->cfg)) {
        FT_ERROR("tsf %s platform init err\n", tsf->name);
        return FT_FAIL;
    }

    if (tsf->cfg.compress_type != FT_COMPRESS_NONE) {
        /* 任一tsf需要压缩，即开启compress压缩线程 */
        ctx->comp_enable = 1;
    }

    /* 初始化读写锁 */
    pthread_rwlock_init(&tsf->rwlock, NULL);

    tsf->name_len = strlen(tsf->name);
    ctx->tsf_idx[ctx->tsf_cnt++] = tsf->type;
    ctx->tsf[tsf->type] = tsf;

    return FT_OK;
}

int ft_transfer_type_get(char *tsf_name)
{
    uint16_t i;
    struct ft_transfer *tsf;
    struct ft_ctx *ctx = &g_ft_ctx;

    for (i = 0; i < ctx->tsf_cnt; i++) {
        tsf = ctx->tsf[ctx->tsf_idx[i]];
        if (!strncmp(tsf->name, tsf_name, tsf->name_len))
            return tsf->type;
    }

    return -1;
}

/* ft_allocator_set 和 ft_malloc/ft_free 现在由 hmemory.c 提供 */

int ft_start(const struct ft_cfg *cfg)
{
    int ret;
    struct ft_ctx *ctx = &g_ft_ctx;

    ret = ft_early_init(ctx, cfg);
    FT_ERR_CHK_RET(ret);

    ret = ft_thread_creat(ctx);
    FT_ERR_CHK_RET(ret);

    return FT_OK;
}

static inline uint32_t __res_info_dump(struct ft_duty_res *res,
                                       char *buf,
                                       uint32_t buf_sz)
{
    uint32_t use_len = 0;

    FT_FORMAT_BUF(buf, buf_sz, use_len, "name#. %s -> %s\r\n", res->src_name,
            res->dst_name);
    FT_FORMAT_BUF(buf, buf_sz, use_len,
    "-----------------------------------------------------------------------------\r\n");

    if (res->pool) {
        FT_FORMAT_BUF(buf, buf_sz, use_len, "[- info_mempool#.]\r\n");
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "get_ok      :%20lu | get_err     :%20lu\r\n",
                res->stat.pool_get_ok, res->stat.pool_get_err);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "put         :%20lu\r\n", res->stat.pool_put);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "-----------------------------------------------------------------------------\r\n");
    }

    if (res->icc_pool) {
        FT_FORMAT_BUF(buf, buf_sz, use_len, "[- icc_mempool#.]\r\n");
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "get_ok      :%20lu | get_err     :%20lu\r\n",
                res->stat.icc_pool_get_ok, res->stat.icc_pool_get_err);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "put         :%20lu\r\n", res->stat.icc_pool_put);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "-----------------------------------------------------------------------------\r\n");
    }

    if (res->ring) {
        FT_FORMAT_BUF(buf, buf_sz, use_len, "[- ring#.]\r\n");
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "enqueue_ok      :%20lu | enqueue_err     :%20lu\r\n",
                res->stat.en_ok, res->stat.en_err);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "dequeue_ok      :%20lu\r\n", res->stat.de_ok);
        FT_FORMAT_BUF(buf, buf_sz, use_len,
                "-----------------------------------------------------------------------------\r\n");
    }
    FT_FORMAT_BUF(buf, buf_sz, use_len, "\r\n");

    return use_len;
}

uint32_t ft_res_info_dump(char *buf, uint32_t buf_sz, enum ft_thread_type type)
{
    if (unlikely(!buf || !buf_sz)) {
        FT_ERROR("buf %p, buf_sz %u err\n", buf, buf_sz);
        return 0;
    }

    uint32_t i;
    uint32_t use_len = 0;
    struct ft_cfg *cfg = &g_ft_ctx.cfg;

    if (type == FT_THREAD_TYPE_MAX || type == FT_THREAD_TYPE_ACQ) {
        /* dump acq concern */
        struct ft_acq_thread *acq = &g_ft_ctx.acq;
        FT_FORMAT_BUF(buf, buf_sz, use_len, "***************\r\n");
        FT_FORMAT_BUF(buf, buf_sz, use_len, "* acq thread: *\r\n");
        FT_FORMAT_BUF(buf, buf_sz, use_len, "***************\r\n");
        use_len += __res_info_dump(&acq->abn.res, buf + use_len, buf_sz - use_len);
        for (i = 0; i < acq->duty_cnt; i++) {
            use_len += __res_info_dump(&acq->duty_res[i], buf + use_len,
                    buf_sz - use_len);
        }
    }

    if (type == FT_THREAD_TYPE_MAX || type == FT_THREAD_TYPE_COMP) {
        /* dump compress concern */
        struct ft_comp_thread *comp;
        for (i = 0; i < cfg->comp_cnt; i++) {
            comp = &g_ft_ctx.comp[i];
            FT_FORMAT_BUF(buf, buf_sz, use_len, "**********************\r\n");
            FT_FORMAT_BUF(buf, buf_sz, use_len, "* compress thread%u:  *\r\n",
                    comp->th_idx);
            FT_FORMAT_BUF(buf, buf_sz, use_len, "**********************\r\n");
            use_len += __res_info_dump(&comp->acq_res, buf + use_len,
                    buf_sz - use_len);
            use_len += __res_info_dump(&comp->self_res, buf + use_len,
                    buf_sz - use_len);
        }
    }

    if (type == FT_THREAD_TYPE_MAX || type == FT_THREAD_TYPE_UP) {
        /* dump upload concern */
        struct ft_up_thread *up;
        for (i = 0; i < cfg->up_cnt; i++) {
            up = &g_ft_ctx.up[i];
            FT_FORMAT_BUF(buf, buf_sz, use_len, "********************\r\n");
            FT_FORMAT_BUF(buf, buf_sz, use_len, "* upload thread%u:  *\r\n",
                    up->th_idx);
            FT_FORMAT_BUF(buf, buf_sz, use_len, "********************\r\n");
            use_len += __res_info_dump(&up->acq_res, buf + use_len,
                    buf_sz - use_len);
        }
    }

    return use_len;
}

void ft_file_in_info_name_get(struct ft_file_info *info,
                              char **filename,
                              uint32_t *filename_len,
                              uint8_t concern_suf)
{
    /* 区分压缩/非压缩类型获取 */
    if (info->is_comp) {
        struct comp_in_path_info *ipi = &info->icc_hdr.icc_list_hdr->ipi;
        *filename = COMP_IN_FILE_NAME(ipi);
        if (concern_suf)
            *filename_len = COMP_IN_FILE_NAME_LEN(ipi);
        else
            *filename_len = ipi->file_len;
    } else {
        struct ft_file_centre *fc = &info->fc;
        *filename = FT_FILE_NAME(fc);
        if (concern_suf)
            *filename_len = FT_FILE_NAME_LEN(fc);
        else
            *filename_len = fc->file_len;
    }
}

void ft_statistics_clear_all(void)
{
    uint16_t i;
    struct ft_ctx *ctx = &g_ft_ctx;

    struct ft_duty_res *res;
    struct ft_acq_thread *acq = &ctx->acq;
    struct ft_cfg *cfg = &ctx->cfg;

    /* 清空acq线程资源及tsf统计信息 */
    memset(acq->tsf_stat, 0, sizeof(acq->tsf_stat));
    for (i = 0; i < acq->duty_cnt; i++) {
        res = &acq->duty_res[i];
        memset(&res->stat, 0, sizeof(res->stat));
    }

    /* 清空comp线程资源及tsf统计信息 */
    for (i = 0; i < cfg->comp_cnt; i++) {
        res = &ctx->comp[i].acq_res;
        memset(&res->stat, 0, sizeof(res->stat));

        res = &ctx->comp[i].self_res;
        memset(&res->stat, 0, sizeof(res->stat));

        memset(ctx->comp[i].tsf_stat, 0, sizeof(ctx->comp[i].tsf_stat));
    }

    /* 清空upload线程资源统计信息及上报统计信息, tsf统计信息 */
    for (i = 0; i < cfg->up_cnt; i++) {
        res = &ctx->up[i].acq_res;
        memset(&res->stat, 0, sizeof(res->stat));
        memset(ctx->up[i].tsf_stat, 0, sizeof(ctx->up[i].tsf_stat));
        
        /* 重新初始化统计数据，使用统一框架 */
        ft_stat_init_by_idx(FT_STAT_METRIC_UPLOAD, i);
    }
}

void ft_tsf_stat_get(struct ft_tsf_stat *stat, uint32_t type)
{
    int i;
    struct ft_up_thread *up;
    struct ft_comp_thread *comp;
    struct ft_acq_thread *acq;
    struct ft_duty_res *res;
    struct ft_ctx *ctx = &g_ft_ctx;

    memset(stat, 0, sizeof(struct ft_tsf_stat));

    acq = &ctx->acq;
    stat->acq_duty_cnt += acq->tsf_stat[type].acq_duty_cnt;
    stat->acq_abn_cnt += acq->tsf_stat[type].acq_abn_cnt;

    for (i = 0; i < acq->duty_cnt; i++) {
        res = &acq->duty_res[i];
        stat->expect_ori_cnt += res->stat.tsf[type].expect_ori_cnt;
        stat->priv_get_err += res->stat.tsf[type].priv_get_err;
    }

    for (i = 0; i < ctx->cfg.up_cnt; i++) {
        up = &ctx->up[i];
        stat->up_duty_cnt += up->tsf_stat[type].up_duty_cnt;
        stat->up_succeed_cnt += up->tsf_stat[type].up_succeed_cnt;
    }

    for (i = 0; i < ctx->cfg.comp_cnt; i++) {
        comp = &ctx->comp[i];
        stat->comp_duty_cnt += comp->tsf_stat[type].comp_duty_cnt;
    }
}

void ft_up_stat_init(uint8_t up_idx)
{
    /* 检查线程索引是否有效 */
    if (up_idx >= g_ft_ctx.cfg.up_cnt) {
        FT_ERROR("Upload thread index out of bounds: %d, max: %d\n", 
                up_idx, g_ft_ctx.cfg.up_cnt);
        return;
    }

    /* 使用统一框架初始化上传统计 */
    ft_stat_init_by_idx(FT_STAT_METRIC_UPLOAD, up_idx);
}

void ft_up_stat_update(uint8_t up_idx, size_t bytes, uint64_t files)
{
    /* 检查线程索引是否有效 */
    if (up_idx >= g_ft_ctx.cfg.up_cnt) {
        FT_ERROR("Upload thread index out of bounds: %d, max: %d\n", 
                up_idx, g_ft_ctx.cfg.up_cnt);
        return;
    }

    /* 使用统一框架更新上传统计，上传模块不需要比率统计，传入0 */
    ft_stat_update_by_idx(FT_STAT_METRIC_UPLOAD, up_idx, bytes, files, 0.0);
}

void ft_up_stat_get(double *KBps, double *fps, uint8_t up_idx)
{
    /* 检查线程索引是否有效 */
    if (up_idx >= g_ft_ctx.cfg.up_cnt) {
        FT_ERROR("Upload thread index out of bounds: %d, max: %d\n", 
                up_idx, g_ft_ctx.cfg.up_cnt);
        if (KBps) *KBps = 0;
        if (fps) *fps = 0;
        return;
    }

    /* 检查输出参数有效性 */
    if (!KBps && !fps) {
        FT_ERROR("All output parameters are NULL\n");
        return;
    }

    double avg_ratio = 0.0; /* 上传模块不使用比率 */
    
    /* 使用统一框架查询上传统计 */
    int ret = ft_stat_get_by_idx(FT_STAT_METRIC_UPLOAD, up_idx, KBps, fps, &avg_ratio);
    
    /* 如果查询失败，返回0 */
    if (ret != 0) {
        if (KBps) *KBps = 0;
        if (fps) *fps = 0;
    }
}
