ft_includes += global_includes
ft_includes += comp_includes
ft_includes += include_directories('.','include')
ft_includes += include_directories('../common/include')

ft_deps = [thread,curl]
ft_link_libs = [comp_lib]
#ft_largs = ['-L/usr/local/lib', '-lhlib']
ft_largs = ['-lrte_mempool', '-lrte_ring', '-lrte_eal']

subdir('include')

ft_sources += files(
    'ft.c',
    'ft_acquire.c',
    'ft_compress.c',
    'ft_upload.c',
    'ft_ops.c',
    'ft_mproc.c',
)

ft_lib = shared_library(
    'hfiletrans',
    ft_sources,
    c_args: global_cflags,
    include_directories: ft_includes,
    dependencies: ft_deps,
    link_with: ft_link_libs,
    link_args: ft_largs,
    version: ft_major_ver,
    soversion: ft_so_version,
    install: true,
)

ft_libraries += ft_lib

hfiletrans = declare_dependency(include_directories: 'include', link_with: ft_lib)
