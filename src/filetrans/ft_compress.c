#include <sys/types.h>
#include "ft.h"

/* compress通过duty资源获取的文件数 */
static inline void ft_tsf_stat_comp_duty_inc(struct ft_comp_thread *comp, uint32_t type)
{
    struct ft_comp_tsf_stat *stat = &comp->tsf_stat[type];
    stat->comp_duty_cnt++;
}

/* 删除已压缩成功的文件 */
void ft_comp_file_remove(struct ft_file_info *info)
{
    if (!info->is_comp)
        return;

    char comp_name[FT_MAX_LEN_256];
    ft_outfile_local_name_creat(info, comp_name, sizeof(comp_name));
    if (0 > remove(comp_name)) {
        FT_ERROR("%s remove err(ft_comp_info_put_upload fail)\n", comp_name);
    }
}

static inline int ft_comp_info_put_upload(struct ft_comp_thread *comp,
                                          struct ft_file_info *info)
{
    /* 入队失败交由异常处理队列 */
    return ft_res_entry_put(&comp->self_res, info, 1);
}

#if 0
static inline void __comp_truck_init(struct ft_comp_truck *truck)
{
    truck->new_fname[0] = 0;
    truck->comp_name[0] = 0;
}
#endif

#ifdef DEBUG
static void __comp_data_process_debug(struct ft_file_info *info,
                                      double diff_time,
                                      uint8_t start)
{
    struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
    if (COMPRESS_INPUT_FILE == info->icc_hdr.type) {
        if (start) {
            FT_DEBUG("Start Compress file %s, file size: %zu\n", icc->ipi.path,
                    icc->filestat.st_size);
        } else {
            FT_DEBUG("End Compress file %s, file size: %u, time(s): %f\n",
                    icc->ipi.path, info->occ.buf_sz, diff_time);
        }
    } else if (COMPRESS_INPUT_DIR == info->icc_hdr.type) {
        if (start) {
            FT_DEBUG("Start Compress dir %s\n", icc->ipi.path);
        } else {
            FT_DEBUG("End Compress dir %s, file size: %u, time(s): %f\n",
                    icc->ipi.path, info->occ.buf_sz, diff_time);
        }
    } else {
        if (start) {
            FT_DEBUG("Start Compress file list(%s..)\n", icc->ipi.path);
        } else {
            FT_DEBUG("End Compress file list(%s..), file size: %u, time(s): %f\n",
                    icc->ipi.path, info->occ.buf_sz, diff_time);
        }
    }
}
#endif

static int ft_compress_data_process(struct ft_comp_thread *comp,
                                    struct ft_transfer *tsf,
                                    struct ft_file_info *info)
{
    int ret;
    struct ft_comp_truck truck;

    /* 各transfer comp_pre hook */
    if (tsf->hook[FT_FILE_COMP_PRE]) {
        ret = tsf->hook[FT_FILE_COMP_PRE](tsf, info, &truck);
        if (unlikely(ret != FT_OK)) {
            FT_ERROR("info %s: hook comp_pre err\n", ft_get_info_path(info));
            goto err;
        }
    }

    /* 依据压缩器类型开始压缩 */
#ifdef DEBUG
    time_t start, end;
    start = time(NULL);
    __comp_data_process_debug(info, 0, 1);
#endif

    /* 压缩处理 */
    ret = compress_dispose_file(comp->compressor, &info->icc_hdr, &info->occ);
    if (unlikely(ret != COMP_OK)) {
        FT_ERROR("Compress file %s err, flag %u, ret %d\n", ft_get_info_path(info),
                info->icc_hdr.type, ret);
        goto err;
    }

    /* 更新压缩统计 */
    size_t input_bytes = 0;
    size_t output_bytes = info->occ.buf_sz;

    /* 计算输入文件总大小 */
    struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
    while (icc) {
        input_bytes += icc->filestat.st_size;
        icc = icc->next;
    }

    /* 更新压缩统计数据 */
    ft_comp_stat_update(comp->th_idx, input_bytes, output_bytes, 1);

#ifdef DEBUG
    end = time(NULL);
    __comp_data_process_debug(info, difftime(end, start), 0);
#endif

    /* 各transfer comp_back hook */
    if (tsf->hook[FT_FILE_COMP_BACK]) {
        ret = tsf->hook[FT_FILE_COMP_BACK](tsf, info, &truck);
        if (unlikely(ret != FT_OK)) {
            FT_ERROR("info %s(flag %u): hook comp_back err\n", ft_get_info_path(info),
                    info->icc_hdr.type);
            goto err;
        }
    }

    ret = ft_comp_info_put_upload(comp, info);
    if (unlikely(ret != FT_OK))
        ft_comp_file_remove(info);  //入队失败，删除当前压缩文件

    return ret;

err:
    ft_file_info_obj_put(info);
    return FT_FAIL;
}

void *ft_compress_process(void *arg)
{
    struct ft_comp_thread *comp = arg;

    /* 设置亲和性/线程名 */
    if (FT_OK != ft_thread_priv_attr_set(comp->bind_core, comp->name)) {
        FT_EXIT(-1);
    }

    struct ft_transfer *tsf;
    struct ft_file_info *info;
    while (1) {
        if (FT_OK != ft_res_entry_get(&comp->acq_res, (void **)&info))
            continue;
        ft_tsf_stat_comp_duty_inc(comp, info->type);

        /* 压缩及其入队 */
        tsf = comp->tsf[info->type];
        ft_compress_data_process(comp, tsf, info);
    }

    return NULL;
}

static int ft_thread_compress_res_creat(struct ft_comp_thread *comp, int idx)
{
#define FT_COMP_RING_SZ_DEF      65536

    /* 对接acq/up线程队列长度默认即可(可能需要调整) */
    uint32_t r_sz = FT_COMP_RING_SZ_DEF;
    char name[FT_MAX_LEN_64];
    struct ft_duty_res *res;

    res = &comp->acq_res;
    /* 创建单生产单消费类型队列 */
    snprintf(name, sizeof(name), "comp_duty_acq_ring_%d", idx);
    res->ring = rte_ring_create(name, r_sz, SOCKET_ID_ANY,
                                RING_F_SP_ENQ | RING_F_SC_DEQ);
    if (!res->ring) {
        FT_ERROR("rte_ring_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }
    /* acq -> === -> comp*/
    ft_thread_res_name_set(res, comp->name, 0);
    ft_thread_res_name_set(res, FT_ACQ_THREAD_NAME, 1);

    res = &comp->self_res;
    /* 创建单生产单消费类型队列 */
    snprintf(name, sizeof(name), "up_duty_comp_ring_%d", idx);
    res->ring = rte_ring_create(name, r_sz, SOCKET_ID_ANY,
                                RING_F_SP_ENQ | RING_F_SC_DEQ);
    if (!res->ring) {
        FT_ERROR("rte_ring_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }
    /* comp -> === -> upload*/
    ft_thread_res_name_set(res, comp->name, 1);

    return FT_OK;
}

static uint8_t ft_comp_level_conv(uint8_t level)
{
    switch (level) {
        case FT_COMPRESS_LEVEL_FASTEST:
            return COMPRESS_LEVEL_FASTEST;
        case FT_COMPRESS_LEVEL_MEDIUM:
            return COMPRESS_LEVEL_MEDIUM;
        case FT_COMPRESS_LEVEL_SLOW:
            return COMPRESS_LEVEL_SLOW;
        case FT_COMPRESS_LEVEL_SLOWEST:
            return COMPRESS_LEVEL_SLOWEST;
        case FT_COMPRESS_LEVEL_NONE:
            return COMPRESS_LEVEL_NONE;
        default:
            return COMPRESS_LEVEL_MEDIUM;
    }
}

static int ft_compressor_creat(struct ft_comp_thread *comp, struct ft_cfg *cfg)
{
    uint8_t level = ft_comp_level_conv(cfg->comp_level);

    /* 不需要压缩内容时，选择deflate */
    if (cfg->comp_level == FT_COMPRESS_LEVEL_NONE)
        comp->compressor = compressor_creat(COMPRESSOR_TYPE_DEFLATE, level);
    else
        comp->compressor = compressor_creat(COMPRESSOR_TYPE_ISAL, level);

    if (!comp->compressor)
        return FT_FAIL;
    return FT_OK;
}

static int ft_thread_compress_early_init(struct ft_ctx *ctx)
{
    int ret;
    int i;
    struct ft_cfg *cfg = &ctx->cfg;
    struct ft_comp_thread *comp;

    for (i = 0; i < cfg->comp_cnt; i++) {
        comp = &ctx->comp[i];
        comp->th_idx = i;
        comp->bind_core = cfg->comp_core[i];
        comp->tsf = ctx->tsf;
        snprintf(comp->name, sizeof(comp->name), "ft_compress_%u", comp->th_idx);

        /* 创建压缩器 */
        ret = ft_compressor_creat(comp, cfg);
        FT_ERR_CHK_RET(ret);

        /* 创建资源 */
        ret = ft_thread_compress_res_creat(comp, i);
        FT_ERR_CHK_RET(ret);

        /* 初始化压缩统计 */
        ret = ft_comp_stat_init(comp->th_idx);
        if (ret != 0) {
            FT_ERROR("compress thread[%d] stat init failed\n", i);
            return FT_FAIL;
        }
    }

    ctx->comp_init = 1;
    return FT_OK;
}

int ft_thread_compress_creat(struct ft_ctx *ctx)
{
    int i;
    int ret;
    pthread_t tid;
    struct ft_cfg *cfg = &ctx->cfg;

    if (!ctx->comp_enable) {
        ctx->comp_init = 1;
        return FT_OK;
    }

    /* 线程早期资源初始化 */
    ret = ft_thread_compress_early_init(ctx);
    FT_ERR_CHK_RET(ret);

    struct ft_comp_thread *comp;
    for (i = 0; i < cfg->comp_cnt; i++) {
        comp = &ctx->comp[i];
        ret = pthread_create(&tid, NULL, ft_compress_process, comp);
        if (ret < 0) {
            FT_ERROR("filetrans compress thread[idx:%d, lcore:%u] create error\n",
                    i, comp->bind_core);
            return FT_FAIL;
        } else {
            FT_INFO("filetrans compress thread[idx:%d, lcore:%u] create\n",
                    i, comp->bind_core);
            pthread_detach(tid);
        }
    }

    return FT_OK;
}

