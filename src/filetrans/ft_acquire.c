#include "ft_mproc.h"
#include "hmemory.h"

#define FT_INFO_PRIV_SPLIT      "|"

static pthread_mutex_t g_res_lock = PTHREAD_MUTEX_INITIALIZER;

static uint8_t ft_get_acq_sig(void)
{
    return g_ft_ctx.acq.sig_flag;
}

/* 信号安全的文件写入函数 */
static int __sig_safe_write(int fd, const char *buf, size_t len)
{
    int ret;
    size_t wr_len = 0;

    while (wr_len < len) {
        ret = write(fd, buf + wr_len, len - wr_len);
        if (ret < 0) {
            if (errno == EINTR) //被信号中断则重试
                continue;
            return FT_FAIL;
        }
        wr_len += ret;
    }
    return FT_OK;
}

static int __ft_safe_abn_rewrite_file(struct ft_abnormal *abn)
{
    int ret = -1;
    int old_fd, new_fd = -1;
    int nread;
    char tmp_path[FT_MAX_LEN_512];
    char buf[FT_MAX_LEN_8192];
    struct ft_cfg *cfg = &g_ft_ctx.cfg;

    /* 创建唯一的临时文件名 */
    snprintf(tmp_path, sizeof(tmp_path), "%s/.%s.tmp.%d", cfg->path,
            FT_ABN_INFO_RECORD, getpid());

    /* 使用低级文件IO */
    if ((old_fd = fileno(abn->fp)) < 0)
        goto cleanup;
    if ((new_fd = open(tmp_path, O_WRONLY|O_CREAT|O_TRUNC, 0644)) < 0)
        goto cleanup;

    /* 定位到未处理的数据位置 */
    if (lseek(old_fd, abn->off, SEEK_SET) == (off_t)-1)
        goto cleanup;

    /* 复制未处理的数据 */
    while ((nread = read(old_fd, buf, sizeof(buf))) > 0) {
        if (__sig_safe_write(new_fd, buf, nread) < 0)
            goto cleanup;
        if (unlikely(ft_get_acq_sig()))  // 及时响应信号
            goto cleanup;
    }
    if (nread < 0)
        goto cleanup;

    /* 确保数据落盘 */
    if (fsync(new_fd) < 0)
        goto cleanup;

    /* 回退方案：先关闭文件再重命名 */
    close(new_fd);
    new_fd = -1;
    if (rename(tmp_path, abn->info.file) == 0) {
        ret = 0;
        fclose(abn->fp);
        abn->fp = fopen(abn->info.file, "a+");  //重新打开文件
    }

cleanup:
    if (new_fd >= 0)
        close(new_fd);
    if (ret != 0)
        unlink(tmp_path); // 失败时清理临时文件

    return ret;
}

static int ft_tsf_valid_check(uint32_t log_type)
{
    if (unlikely(log_type >= FT_TRANSFER_TYPE_MAX || !g_ft_ctx.tsf[log_type])) {
        FT_DEBUG("log_type %u err, tsf is empty\n", log_type);
        return 0;
    }
    return 1;
}

#if 0
static int ft_tsf_attr_valid_check(struct ft_info_attr *attr, uint32_t log_type)
{
    struct ft_transfer *tsf = g_ft_ctx.tsf[log_type];
    uint8_t plat = attr->platform;

    /* 上报平台无任何服务器信息时，无效 */
    if (unlikely(!tsf->cfg.platform[plat].srv_cnt)) {
        FT_DEBUG("tsf %s: plat %u srv_cnt %u\n", tsf->name, plat,
                tsf->cfg.platform[plat].srv_cnt);
        return 0;
    }

    return 1;
}
#endif

/* 依据分隔符获取下个字段 */
static char *__ft_next_field(char **str, char delim)
{
    if (!str || !*str)
        return NULL;

    char *start = *str;
    char *end = strchr(start, delim);
    if (end) {
        *end = 0;
        *str = end + 1;
    } else {
        *str = NULL;
    }

    return start;
}


static void *__ft_safe_strtol(const char *str, uint32_t *v)
{
    char *end;

    errno = 0;
    uint32_t num = strtol(str, &end, 10);

    if (end == str || errno == ERANGE) {
        FT_ERROR("%s convert num err\n", str);
        return NULL;
    }

    *v = num;
    return (void*)str;
}

static void *__ft_safe_strtoll(const char *str, uint64_t *v)
{
    char *end;

    errno = 0;
    uint64_t num = strtoll(str, &end, 10);

    if (end == str || errno == ERANGE) {
        FT_ERROR("%s convert num err, num = %lu\n", str, num);
        return NULL;
    }

    *v = num;
    return (void*)str;
}

/* 预期处理文件统计信息 */
static inline void ft_tsf_stat_expect_ori_inc(struct ft_duty_res *res, uint32_t type)
{
    struct ft_duty_tsf_stat *stat = &res->stat.tsf[type];
    stat->expect_ori_cnt++;
}

/* 预期处理文件统计信息 */
static inline void ft_tsf_stat_priv_err_inc(struct ft_duty_res *res, uint32_t type)
{
    struct ft_duty_tsf_stat *stat = &res->stat.tsf[type];
    stat->priv_get_err++;
}

/* acquire通过duty资源获取的文件数 */
static inline void ft_tsf_stat_acq_duty_inc(struct ft_acq_thread *acq, uint32_t type)
{
    struct ft_acq_tsf_stat *stat = &acq->tsf_stat[type];
    stat->acq_duty_cnt++;
}

/* acquire通过异常记录文件获取的文件数 */
static inline void ft_tsf_stat_acq_abn_inc(struct ft_acq_thread *acq, uint32_t type)
{
    struct ft_acq_tsf_stat *stat = &acq->tsf_stat[type];
    stat->acq_abn_cnt++;
}

/*
 * 异常处理记录文件及其细节记录均存在时，后续需基于此持续处理.
 */
static int ft_acq_abn_detail_init(struct ft_abnormal *abn)
{
    /* 任一文件不存在时，不做处理 */
    if (0 != access(abn->info.file, F_OK) || 0 != access(abn->detail_file, F_OK))
        return FT_OK;

    /* 如果本次服务之前有异常信息未加载，需持续处理 */
    abn->fp = fopen(abn->info.file, "a+");
    if (unlikely(!abn->fp)) {
        FT_ERROR("%s open err\n", abn->info.file);
        return FT_OK;
    }

    FILE *fp = fopen(abn->detail_file, "r");
    if (unlikely(!fp)) {
        FT_ERROR("%s open err\n", abn->detail_file);
        return FT_OK;
    }

    /* 异常记录文件及其异常处理信息均存在,处理细节信息 */
    char *p;
    char buf[FT_MAX_LEN_1024];
    char *cur = buf;
    if (!fgets(buf, sizeof(buf), fp)) {
        goto err;
    }

    /* off */
    //p = strtok(buf, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p || !__ft_safe_strtoll(p, &abn->off)))
        goto err;

    /* deal_line */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p || !__ft_safe_strtoll(p, &abn->info.deal_line)))
        goto err;

    /* total_line */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p || !__ft_safe_strtoll(p, &abn->info.total_line)))
        goto err;

    fclose(fp);
    return FT_OK;

err:
    if (fp)
        fclose(fp);
    if (abn->fp) {
        fclose(abn->fp);
        abn->fp = NULL;
    }
    FT_ERROR("%s parse err\n", abn->detail_file);
    return FT_FAIL;
}

static int ft_acq_abnormal_init(struct ft_abnormal *abn, struct ft_cfg *cfg)
{
#define FT_ABN_RING_SZ_DEF      65536
#define FT_ABN_POOL_CNT_DEF      65536
    char name[FT_MAX_LEN_64];
    uint32_t r_sz = FT_ABN_RING_SZ_DEF;
    uint32_t node_cnt = FT_ABN_POOL_CNT_DEF;
    struct ft_duty_res *res = &abn->res;

    /* 异常处理记录文件 */
    snprintf(abn->info.file, sizeof(abn->info.file), "%s/%s", cfg->path,
            FT_ABN_INFO_RECORD);
    snprintf(abn->detail_file, sizeof(abn->detail_file), "%s/%s", cfg->path,
            FT_ABN_DETAIL_RECORD);
    ft_acq_abn_detail_init(abn);

#if 0
    /* 创建file_info内存池（多生产多消费）*/
    size_t sz = node_cnt * sizeof(struct ft_file_info);
    void *mem = hmem_malloc(sz);
    if (!mem) {
        FT_ERROR("abnormal file_info pool hmem_malloc failed\n");
        return FT_FAIL;
    }

    snprintf(name, sizeof(name), "acq_abn_fi_pool");
    int ret = HLIB_Mempool_Init_By_Ops(&res->pool, name, mem,
            sizeof(struct ft_file_info), node_cnt, 0, 0);
    if (ret != 0) {
        FT_ERROR("%s create failed\n", name);
        return FT_FAIL;
    }

    /* 创建压缩文件链节点内存池（多生产多消费）, file_info的2倍 */
    sz = node_cnt * 2 * sizeof(struct comp_input_centre);
    mem = hmem_malloc(sz);
    if (!mem) {
        FT_ERROR("abnormal icc pool hmem_malloc failed\n");
        return FT_FAIL;
    }

    snprintf(name, sizeof(name), "acq_abn_icc_pool");
    ret = HLIB_Mempool_Init_By_Ops(&res->icc_pool, name, mem,
            sizeof(struct comp_input_centre), node_cnt * 2, 0, 0);
    if (ret != 0) {
        FT_ERROR("%s create failed\n", name);
        return FT_FAIL;
    }
#endif

    /* 创建file_info内存池（多生产多消费）*/
    snprintf(name, sizeof(name), "acq_abn_fi_pool");
    res->pool = rte_mempool_create(name, node_cnt, sizeof(struct ft_file_info),
                                   0, 0, NULL, NULL, NULL, NULL, SOCKET_ID_ANY, 0);
    if (!res->pool) {
        FT_ERROR("rte_mempool_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }

    /* 创建压缩文件链节点内存池（多生产多消费）, file_info的2倍 */
    snprintf(name, sizeof(name), "acq_abn_icc_pool");
    res->icc_pool = rte_mempool_create(name, node_cnt * 2,
                                       sizeof(struct comp_input_centre), 0, 0, NULL, NULL, NULL, NULL,
                                       SOCKET_ID_ANY, 0);
    if (!res->icc_pool) {
        FT_ERROR("rte_mempool_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }

    /* output/compress/upload -> === -> acq */
    snprintf(name, sizeof(name), "{output sth/compress sth/upload sth} (abnormal)");
    ft_thread_res_name_set(res, name, 1);
    snprintf(name, sizeof(name), "%s (abnormal dispose)", FT_ACQ_THREAD_NAME);
    ft_thread_res_name_set(res, name, 0);

    /* 创建多生产单消费类型队列 */
    snprintf(name, sizeof(name), "acq_abn_ring");
    res->ring = rte_ring_create(name, r_sz, SOCKET_ID_ANY, RING_F_SC_DEQ);
    if (!res->ring) {
        FT_ERROR("rte_ring_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }

    return FT_OK;
}

static void *__acquire_res_creat(struct ft_acq_thread *acq,
                                 uint32_t ring_sz,
                                 uint32_t pool_node_cnt,
                                 char *th_name,
                                 uint8_t is_public)
{
#define FT_ACQ_RING_SZ_DEF      65536
#define FT_ACQ_POOL_CNT_DEF     65536

    if (acq->duty_cnt >= FT_ACQ_DUTY_CNT_MAX) {
        FT_ERROR("acq duty_cnt %u >= max %u, failed\n", acq->duty_cnt,
                FT_ACQ_DUTY_CNT_MAX);
        return NULL;
    }

    uint32_t r_sz = ring_sz ? ring_sz : FT_ACQ_RING_SZ_DEF;
    uint32_t node_cnt = pool_node_cnt ? pool_node_cnt : FT_ACQ_POOL_CNT_DEF;
    struct ft_duty_res *res = &acq->duty_res[acq->duty_cnt];

    char name[FT_MAX_LEN_64];

#if 0
    if (is_public)
        node_cnt *= 2;
#endif

    /* output -> === -> acq */
    ft_thread_res_name_set(res, th_name, 1);
    ft_thread_res_name_set(res, FT_ACQ_THREAD_NAME, 0);

    /* 创建单消费类型file_info池（入队失败会涉及多生产场景）*/
    snprintf(name, sizeof(name), "acq_dutypool_fi_%u", acq->duty_cnt);
    res->pool = rte_mempool_create(name, node_cnt, sizeof(struct ft_file_info),
                                   0, 0, NULL, NULL, NULL, NULL, SOCKET_ID_ANY,
                                   is_public ? 0 : MEMPOOL_F_SC_GET);
    if (!res->pool) {
        FT_ERROR("rte_mempool_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return NULL;
    }

    /* 创建单消费类型file_centre池（入队失败会涉及多生产场景）, file_info的2倍 */
    snprintf(name, sizeof(name), "acq_dutypool_icc_%u", acq->duty_cnt);
    res->icc_pool = rte_mempool_create(name, node_cnt * 2,
                                       sizeof(struct comp_input_centre), 0, 0, NULL, NULL, NULL, NULL,
                                       SOCKET_ID_ANY, is_public ? 0 : MEMPOOL_F_SC_GET);
    if (!res->icc_pool) {
        FT_ERROR("rte_mempool_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return NULL;
    }

#if 0
    /* 创建单消费类型file_info池（入队失败会涉及多生产场景）*/
    size_t sz = node_cnt * sizeof(struct ft_file_info);
    void *mem = hmem_malloc(sz);
    if (!mem) {
        FT_ERROR("acq file_info pool hmem_malloc failed\n");
        return NULL;
    }
    snprintf(name, sizeof(name), "acq_dutypool_fi_%u", acq->duty_cnt);
    int ret = HLIB_Mempool_Init_By_Ops(&res->pool, name, mem,
            sizeof(struct ft_file_info),
            node_cnt, 0, is_public ? 0 : HLIB_MEMPOOL_F_SC_GET);
    if (ret != 0) {
        FT_ERROR("%s create failed\n", name);
        return NULL;
    }

    /* 创建单消费类型file_centre池（入队失败会涉及多生产场景）, file_info的2倍 */
    sz = node_cnt * 2 * sizeof(struct comp_input_centre);
    mem = hmem_malloc(sz);
    if (!mem) {
        FT_ERROR("acq icc pool hmem_malloc failed\n");
        return NULL;
    }
    snprintf(name, sizeof(name), "acq_dutypool_icc_%u", acq->duty_cnt);
    ret = HLIB_Mempool_Init_By_Ops(&res->icc_pool, name, mem,
            sizeof(struct comp_input_centre), node_cnt * 2, 0,
            is_public ? 0 : HLIB_MEMPOOL_F_SC_GET);
    if (ret != 0) {
        FT_ERROR("%s create failed\n", name);
        return NULL;
    }
#endif

    /* 创建单生产单消费类型队列 */
    snprintf(name, sizeof(name), "acq_dutyring_%u", acq->duty_cnt);
    res->ring = rte_ring_create(name, r_sz, SOCKET_ID_ANY,
                                is_public ? RING_F_SC_DEQ : (RING_F_SP_ENQ | RING_F_SC_DEQ));
    if (!res->ring) {
        FT_ERROR("rte_ring_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return NULL;
    }

    acq->duty_cnt++;

    return res;
}

void *ft_acquire_res_creat_op(uint32_t ring_sz,
                              uint32_t pool_node_cnt,
                              char *th_name,
                              uint8_t is_public)
{
    void *p;
    struct ft_acq_thread *acq = &g_ft_ctx.acq;

    pthread_mutex_lock(&g_res_lock);
    p = __acquire_res_creat(acq, ring_sz, pool_node_cnt, th_name, is_public);
    pthread_mutex_unlock(&g_res_lock);

    return p;
}

void *ft_acquire_res_creat(uint32_t ring_sz, uint32_t pool_node_cnt, char *th_name)
{
    return ft_acquire_res_creat_op(ring_sz, pool_node_cnt, th_name, 0);
}

static void *ft_acquire_res_creat_public(uint32_t ring_sz,
                                         uint32_t pool_node_cnt,
                                         char *th_name)
{
    return ft_acquire_res_creat_op(ring_sz, pool_node_cnt, th_name, 1);
}

#if 0
static void *ft_comp_icc_obj_get_abn(void *pool)
{
    struct ft_duty_res *res = &g_ft_ctx.acq.abn.res;
    /* 尝试在异常内存池获取obj */
    if (pool == (void*)&res->icc_pool)
        return NULL;
    return ft_comp_icc_obj_get(res);
}
#endif

static void *ft_file_info_obj_get_abn(void *pool, uint32_t log_type, uint8_t input_type)
{
    struct ft_duty_res *res = &g_ft_ctx.acq.abn.res;

    /* 尝试在异常内存池获取obj */
    if (pool == (void*)&res->pool)
        return NULL;
    return ft_file_info_obj_get(res, log_type, input_type);
}

static void *ft_comp_icc_creat(struct ft_duty_res *res)
{
    if (unlikely(!res)) {
        FT_ERROR("res is NULL\n");
        return NULL;
    }
#if 0
    struct comp_input_centre *icc = ft_comp_icc_obj_get(res);
    if (unlikely(!icc)) {
        icc = ft_comp_icc_obj_get_abn(&res->icc_pool);
        if (unlikely(!icc))
            return NULL;
    }
    return icc;
#endif
    return ft_comp_icc_obj_get(res);
}

/**
 * 绝对路径解析
 *
 * @param fc
 *  文件核心信息.
 * @param path
 *  绝对路径.
 * @return
 *  FT_OK: 解析成功.
 *  FT_FAIL: 解析失败.
 */
int file_info_path_parse(struct ft_file_centre *fc, char *path)
{
    size_t len = strlen(path);
    if (unlikely(len >= sizeof(fc->path))) {
        FT_ERROR("path(%s) is too lang\n", path);
        return FT_FAIL;
    }

    FT_DEBUG("path = %s\n", path);

    char *f;
    char *suf;
    memcpy(fc->path, path, len);
    fc->path[len] = 0;
    f = strrchr(fc->path, '/') + 1;   //文件名不含 '/'
    if (unlikely(!f)) {
        FT_ERROR("path(%s) has no / \n", path);
        return FT_FAIL;
    }
    suf = strrchr(f, '.');            //后缀名含 '.'，可考虑扩展 '_'等后缀
    if (!suf) {
        fc->suffix_len = 0;
    } else {
        fc->suffix_len = strlen(suf);
    }

    fc->file_len = strlen(f) - fc->suffix_len;
    fc->path_len = f - fc->path;

    return FT_OK;
}

static void *ft_file_info_creat(struct ft_duty_res *res,
                                uint32_t log_type,
                                uint8_t input_type)
{
    if (unlikely(!res)) {
        FT_ERROR("log_type %u res is NULL\n", log_type);
        return NULL;
    }

    struct ft_file_info *info = ft_file_info_obj_get(res, log_type, input_type);
    if (unlikely(!info)) {
        info = ft_file_info_obj_get_abn(&res->pool, log_type, input_type);
        if (unlikely(!info)) {
            FT_ERROR("%u obj get_abn err\n", log_type);
            return NULL;
        }
    }
    return info;
}

void *ft_file_list_creat(uint32_t log_type, void *out_res)
{
    if (!ft_tsf_valid_check(log_type))
        return NULL;

    uint8_t comp_type = ft_get_fi_comp_type(log_type);
    if (unlikely(comp_type == FT_COMPRESS_NONE || comp_type == FT_COMPRESS_GZIP)) {
        /* 创建文件列表,必须是压缩方式 */
        FT_ERROR("log_type %u compress_type is %u, unsupported file list\n",
                log_type, comp_type);
        return NULL;
    }

    if (unlikely(!out_res))
        out_res = g_ft_ctx.acq.public_res;

    /* 创建file_info */
    struct ft_file_info *info =
        ft_file_info_creat(out_res, log_type, COMPRESS_INPUT_LIST);
    if (unlikely(!info)) {
        FT_ERROR("%u file_info creat err(list)\n", log_type);
    }

    return info;
}

static inline int __file_node_add(struct ft_file_info *info,
                                  char *path,
                                  void *res,
                                  uint8_t fail_free)
{
    if (unlikely(!info->is_comp)) {
        /* 普通文件 */
        FT_INFO("%s not compress mode\n", path);
        return FT_FAIL;
    }

    /* 压缩 */
    uint8_t flag = info->icc_hdr.type;
    if (unlikely(0 != access(path, F_OK))) {
        FT_INFO("%s not exist(%s)\n", path,
                (flag == COMPRESS_INPUT_DIR) ? "dir": "file");
        goto err;
    }

    if (!info->icc_hdr.icc_list_hdr)
        info->icc_res = res;
        //info->icc_pool = &(((struct ft_duty_res*)res)->icc_pool);

    struct comp_input_centre *icc = ft_comp_icc_creat(res);
    if (unlikely(!icc)) {
        FT_ERROR("%s icc get err\n", path);
        goto err;
    }
    if (COMP_OK != comp_input_path_parse(&icc->ipi, path, 0, flag)) {
        ft_comp_icc_obj_put(icc, info->icc_res);
        goto err;
    }

    /* 挂链 */
    comp_input_centre_add(&info->icc_hdr, icc);
    return FT_OK;

err:
    if (fail_free) {
        /* 任一列表加入失败，即回收所有内容 */
        ft_file_info_obj_put(info);
    }
    return FT_FAIL;
}

int ft_file_list_add(void *file_list, char *path, void *out_res, uint8_t fail_free)
{
    if (unlikely(!out_res))
        out_res = g_ft_ctx.acq.public_res;

    return __file_node_add(file_list, path, out_res, fail_free);
}

static void *ft_info_list_precond(struct ft_file_info *info, struct ft_info_attr *attr)
{
    /* check */
    if (unlikely(!info->icc_hdr.icc_list_hdr)) {
        /* 空链不处理 */
        FT_ERROR("tsf %u ft icc list is empty\n", info->type);
        goto err;
    }

    /* check */
    if (attr) {
        if (unlikely(attr->platform >= FT_UP_PLATFORM_CNT_MAX)) {
            FT_ERROR("attr platform %u err\n", attr->platform);
            goto err;
        }
    } else {
        /* 必需包含子类型 */
        if (unlikely(g_ft_ctx.tsf[info->type]->cfg.concern_sub)) {
            FT_ERROR("log_type %u concern subtype, attr empty\n", info->type);
            goto err;
        }
    }

    return info;

err:
    ft_file_info_obj_put(info);
    return NULL;
}

static void *ft_info_path_precond(void *res,
                                  char *path,
                                  struct ft_info_attr *attr,
                                  uint32_t log_type,
                                  uint8_t flag)
{
    /* check */
    if (attr) {
        if (unlikely(attr->platform >= FT_UP_PLATFORM_CNT_MAX)) {
            FT_ERROR("attr platform %u err\n", attr->platform);
            return NULL;
        }
    } else {
        if (unlikely(g_ft_ctx.tsf[log_type]->cfg.concern_sub)) {
            FT_ERROR("log_type %u concern subtype, attr empty\n", log_type);
            return NULL;
        }
    }

    uint8_t comp_type = ft_get_fi_comp_type(log_type);
    if (unlikely(flag == COMPRESS_INPUT_DIR && comp_type == FT_COMPRESS_GZIP)) {
        /* 文件夹不支持gzip压缩方式 */
        FT_ERROR("log_type %u compress_type is %u, unsupported\n", log_type, comp_type);
        return NULL;
    }

    /* 创建file_info */
    struct ft_file_info *info = ft_file_info_creat(res, log_type, flag);
    if (unlikely(!info)) {
        FT_ERROR("%s file_info creat err\n", path);
        return NULL;
    }

    /* 依据path，添加node, 若失败，内部回收 */
    if (info->is_comp) {
        if (FT_OK != __file_node_add(info, path, res, 1)) {
            return NULL;
        }
    } else {
        if (FT_OK != file_info_path_parse(&info->fc, path)) {
            ft_file_info_obj_put(info);
            return NULL;
        }
        info->fc_type = flag;   //上传文件或目录内文件
    }

    return info;
}

/* 输入文件类型转为压缩类型 */
static inline int ft_info_to_comp_type(uint8_t type)
{
    int comp_input_type = -1;
    switch (type) {
        case FT_INFO_DIR:
            comp_input_type = COMPRESS_INPUT_DIR;
            break;
        case FT_INFO_FILE:
            comp_input_type = COMPRESS_INPUT_FILE;
            break;
        case FT_INFO_LIST:
            comp_input_type = COMPRESS_INPUT_LIST;
            break;
        default:
            break;
    }
    return comp_input_type;
}

/* info在get时，已初步初始化，在此进行进一步初始化 */
static inline void ft_file_info_further_init(struct ft_info_attr *attr,
                                             struct ft_file_info *info)
{
    /* 保存用户可能携带的相关文件属性 */
    if (attr) {
        memcpy(&info->attr, attr, sizeof(*attr));
    }
}

void *ft_file_info_priv_mem_get(void *res, uint32_t log_type)
{
    /* check */
    if (!ft_tsf_valid_check(log_type))
        return NULL;

    if (unlikely(!res))
        res = g_ft_ctx.acq.public_res;

    struct ft_transfer *tsf = g_ft_ctx.tsf[log_type];
    struct ft_info_attr_priv_hdr *priv_hdr;

    int ret = rte_mempool_get(tsf->info_priv_pool, (void**)&priv_hdr);
    if (unlikely(ret != 0)) {
        ft_tsf_stat_priv_err_inc(res, log_type);
        return NULL;
    }

    priv_hdr->tsf_type = log_type;
    priv_hdr->pool = tsf->info_priv_pool;
    return &priv_hdr->data[0];
}

void ft_file_info_priv_mem_put(void *usr_priv)
{
    if (!usr_priv)
        return;

    struct ft_info_attr_priv_hdr *priv_hdr;

    priv_hdr = (void*)((char*)usr_priv - FT_INFO_ATTR_PRIV_HDR_SZ);
    rte_mempool_put(priv_hdr->pool, priv_hdr);
}

int ft_file_info_priv_pool_creat(struct ft_transfer *tsf,
                                 uint32_t priv_sz,
                                 uint32_t pool_node_cnt)
{
#define FT_INFO_PRIV_POOL_CNT_DEF     65536
    /* 创建单消费类型file_info_usr_priv池（入队失败会涉及多生产场景）*/
    uint32_t node_cnt = pool_node_cnt ? pool_node_cnt : FT_INFO_PRIV_POOL_CNT_DEF;
    uint32_t priv_total_sz = FT_INFO_ATTR_PRIV_HDR_SZ + priv_sz;
    char name[FT_MAX_LEN_128];

    snprintf(name, sizeof(name), "u_prv_%s", tsf->name); // 用户私有数据内存池名称
    tsf->info_priv_pool = rte_mempool_create(name, node_cnt, priv_total_sz,
            0, 0, NULL, NULL, NULL, NULL, SOCKET_ID_ANY, 0);
    if (!tsf->info_priv_pool) {
        FT_ERROR("rte_mempool_create %s failed, rte_errno:%d(%s)\n", name, rte_errno, rte_strerror(rte_errno));
        return FT_FAIL;
    }
    tsf->info_priv_sz = priv_sz;

#if 0
    size_t sz = node_cnt * priv_total_sz;
    //size_t sz = node_cnt * FT_ALIGN(priv_total_sz, 16);

    char *mem = hmem_malloc(sz + sizeof(HLIB_Mempool_t));
    if (!mem) {
        FT_ERROR("tsf %s: file_info_usr_priv pool hmem_malloc failed\n", tsf->name);
        return FT_FAIL;
    }

    tsf->info_priv_pool = (void*)mem;
    mem += sizeof(HLIB_Mempool_t);

    int ret = HLIB_Mempool_Init_By_Ops(tsf->info_priv_pool, name, mem, priv_total_sz,
            node_cnt, 0, 0);
    if (ret != 0) {
        FT_ERROR("%s create failed\n", name);
        return FT_FAIL;
    }
    tsf->info_priv_sz = priv_sz;
#endif

    return FT_OK;
}

static inline void *__ft_info_creat(void *out_res,
                                    void *arg,
                                    struct ft_info_attr *attr,
                                    uint32_t log_type,
                                    uint8_t flag)
{
    /* check */
    if (!ft_tsf_valid_check(log_type)) {
        ft_file_info_priv_mem_put(attr->usr_priv);
        return NULL;
    }

    ft_tsf_stat_expect_ori_inc(out_res, log_type);

    struct ft_file_info *info;
    int info_type = ft_info_to_comp_type(flag);
    if (unlikely(info_type < 0)) {
        FT_ERROR("flag to comp_type err: flag = %u\n", flag);
        ft_file_info_priv_mem_put(attr->usr_priv);
        return NULL;
    }

    /* 文件信息预处理 */
    if (COMPRESS_INPUT_LIST == info_type) {
        info = ft_info_list_precond(arg, attr);
    } else {
        info = ft_info_path_precond(out_res, arg, attr, log_type, info_type);
    }
    if (unlikely(!info)) {
        ft_file_info_priv_mem_put(attr->usr_priv);
        return NULL;
    }

    /* info在get时，已初步初始化，在此进行进一步初始化 */
    ft_file_info_further_init(attr, info);
    return info;
}

int ft_info_put_acq(void *out_res,
                    void *arg,
                    struct ft_info_attr *attr,
                    uint32_t log_type,
                    uint8_t flag)
{
    if (unlikely(!out_res))
        out_res = g_ft_ctx.acq.public_res;

    struct ft_file_info *info = __ft_info_creat(out_res, arg, attr, log_type, flag);
    if (!info)
        return FT_FAIL;

    /*
     * 1. 服务开启时: 若入队失败交由异常处理队列
     * 2. 服务关闭时: 直接交由异常处理队列落入缓存文件，待后续处理
     */
    int ret;
    if (FT_FILETRANS_START == ft_status_get()) {
        ret = ft_res_entry_put(out_res, info, 1);
    } else {
        ret = ft_abnormal_info_put(info);
    }

    if (unlikely(ft_get_acq_sig())) {
        /* 若捕捉信号，开启处理，并在文件信息入口处挂起 */
        ft_status_set(FT_FILETRANS_START);
        while (1) {
            rte_pause();
        }
    }

    return ret;
}

static int ft_file_info_put_upload(struct ft_acq_thread *acq, struct ft_file_info *info)
{
#define FT_ACQ_DISP_BASE_UP_CNT_MAX         1000000
    if (unlikely(acq->disp_base_up >= FT_ACQ_DISP_BASE_UP_CNT_MAX))
        acq->disp_base_up = 0;

    /* 一对多场景，均衡分发 */
    struct ft_ctx *ctx = &g_ft_ctx;
    uint32_t up_idx = (acq->disp_base_up++) % ctx->cfg.up_cnt;
    struct ft_duty_res *res = &ctx->up[up_idx].acq_res;

    /* 若入队失败不会交由异常处理队列 */
    return ft_res_entry_put(res, info, 0);
#undef FT_ACQ_DISP_BASE_UP_CNT_MAX
}

static int ft_file_info_put_compress(struct ft_acq_thread *acq, struct ft_file_info *info)
{
#define FT_ACQ_DISP_BASE_COMP_CNT_MAX         1000000
    if (unlikely(acq->disp_base_comp >= FT_ACQ_DISP_BASE_COMP_CNT_MAX))
        acq->disp_base_comp = 0;

    /* 一对多场景，均衡分发 */
    struct ft_ctx *ctx = &g_ft_ctx;
    uint32_t comp_idx = (acq->disp_base_comp++) % ctx->cfg.comp_cnt;
    struct ft_duty_res *res = &ctx->comp[comp_idx].acq_res;

    /* 若入队失败不会交由异常处理队列 */
    return ft_res_entry_put(res, info, 0);
#undef FT_ACQ_DISP_BASE_COMP_CNT_MAX
}

static inline int ft_acq_file2mem(struct stat *filestat, char **buf, char *path)
{
    int fd = open(path, FT_READ_FLAG);
    if (0 > fd) {
        FT_ERROR("%s open err(%s)\n", path, strerror(errno));
        return FT_FAIL;
    }

    /* 获取文件信息 */
    if (-1 == fstat(fd, filestat)) {
        FT_ERROR("%s get fstat err\n", path);
        close(fd);
        return FT_FAIL;
    }

    int ret = FT_OK;
    if (filestat->st_size < FT_ACQ_MMAP_FILE_SZ_MAX) {
        if (likely(filestat->st_size)) {
            (*buf) = ft_file_mem_mmap(fd, filestat->st_size);
            if (unlikely(!(*buf))) {
                FT_ERROR("%s mmap file err\n", path);
                ret = FT_FAIL;
            }
        } else {
            /* 空文件 */
            ret = FT_FAIL;
            if (0 > remove(path)) {
                FT_ERROR("%s remove err\n", path);
            } else {
                FT_ERROR("%s is empty! remove!\n", path);
            }
        }
    } else {
        FT_DEBUG("%s is Oversized (sz = %zu)\n", path, filestat->st_size);
    }

    close(fd);
    return ret;
}

static int ft_acq_file_centre_init(struct ft_transfer *tsf, struct ft_file_info *info)
{
    if (unlikely(!tsf)) {
        /* 可能是未注册的类型 */
        return FT_FAIL;
    }

    /* file类型:
     * 1. 需要关注文件内容时，可能需要做映射：
     *  a. 超大小后，不映射
     * 2. 不关注文件内容时，仅获取基本文件信息
     * */
    if (info->is_comp) {
        struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
#if 1
        while (icc) {
            if (-1 == stat(icc->ipi.path, &icc->filestat)) {
                FT_ERROR("%s get stat err\n", icc->ipi.path);
                return FT_FAIL;
            }
            icc = icc->next;
        }
#else
        if (info->icc_hdr.type != COMPRESS_INPUT_FILE) {
            /* dir/list类型: 仅获取各文件基本信息 */
            while (icc) {
                if (-1 == stat(icc->ipi.path, &icc->filestat)) {
                    FT_ERROR("%s get stat err\n", icc->ipi.path);
                    return FT_FAIL;
                }
                icc = icc->next;
            }
        } else {
            /* 获取文件信息,映射内存,可能超大小不做映射 */
            if (FT_FAIL == ft_acq_file2mem(&icc->filestat, &icc->buf,
                        icc->ipi.path)) {
                return FT_FAIL;
            }
        }
#endif
    } else {
        /* 目录类型啥也不做 */
        if (info->fc_type == FT_INPUT_DIR)
            return FT_OK;

        struct ft_file_centre *fc = &info->fc;
#if 1
        if (-1 == stat(fc->path, &fc->filestat)) {
            FT_ERROR("%s get stat err\n", fc->path);
            return FT_FAIL;
        }
#else
        if (!tsf->need_content) {
            /* 无需映射内存时，直接获取文件信息 */
            if (-1 == stat(fc->path, &fc->filestat)) {
                FT_ERROR("%s get stat err\n", fc->path);
                return FT_FAIL;
            }
        } else {
            /* 获取文件信息,映射内存 */
            if (FT_FAIL == ft_acq_file2mem(&fc->filestat, &fc->buf,
                        fc->path)) {
                return FT_FAIL;
            }
        }
#endif
    }

    return FT_OK;
}

static inline int __acq_abnormal_info_write(struct ft_acq_thread *acq,
                                            struct ft_abnormal *abn,
                                            struct ft_file_info *info)
{
    /* 若文件已关闭，重新打开 */
    if (!abn->fp) {
        abn->fp = fopen(abn->info.file, "w+");
        if (unlikely(!abn->fp)) {
            FT_ERROR("%s open err\n", abn->info.file);
            ft_file_info_obj_put(info);
            return FT_FAIL;
        }
        /* 初始值 */
        //abn->info.total_line = abn->info.deal_line = abn->off = 0;
    }

    uint32_t ser_len = 0;
    char ser_buf[FT_MAX_LEN_1024];
    if (info->attr.usr_priv) {
        /* 异常需要进行私有参数序列化 */
        struct ft_transfer *tsf = acq->tsf[info->type];
        if (!tsf->ser) {
            FT_ERROR("tsf %s ser func is empty\n", tsf->name);
            return FT_FAIL;
        }

        ser_len = tsf->ser(info->attr.usr_priv, ser_buf, sizeof(ser_buf));
        if (!ser_len) {
            FT_ERROR("tsf %s ser func fail!(ser_len %u)\n", tsf->name, ser_len);
            return FT_FAIL;
        }
    }


    size_t len;
    char buf[FT_MAX_LEN_1024];

    /* 文件尾部追加，强制将缓冲区中的数据写入到文件 */
    fseek(abn->fp, 0, SEEK_END);
    if (!info->is_comp) {
        /* 非压缩方式 */
        len = snprintf(buf, sizeof(buf), "%u,%u,%u,%u,%u,%s", info->type,
                info->attr.platform, info->attr.sub_type, info->attr.line,
                info->fc_type, info->fc.path);
        fwrite(buf, 1, len, abn->fp);
    } else {
        /* 压缩方式的dir/file/list */
        len = snprintf(buf, sizeof(buf), "%u,%u,%u,%u,%u", info->type,
                info->attr.platform, info->attr.sub_type, info->attr.line,
                info->icc_hdr.type);
        fwrite(buf, 1, len, abn->fp);
        struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
        while (icc) {
            fwrite(",", 1, 1, abn->fp);
            fwrite(icc->ipi.path, 1, info->icc_hdr.type == COMPRESS_INPUT_DIR ?
                    COMP_IN_DIR_PATH_ALL_LEN(&icc->ipi) :
                    COMP_IN_FILE_PATH_ALL_LEN(&icc->ipi), abn->fp);
            icc = icc->next;
        }
    }

    if (info->attr.usr_priv) {
        /* 写入序列化数据 */
        fwrite(FT_INFO_PRIV_SPLIT, 1, 1, abn->fp);
        fwrite(ser_buf, 1, ser_len, abn->fp);
    }

    fwrite("\n", 1, 1, abn->fp);

    fflush(abn->fp);
    fsync(fileno(abn->fp)); //同步磁盘
    abn->info.total_line++;

    /* 处理结束立即回收 */
    ft_file_info_obj_put(info);

    return FT_OK;
}

static int ft_acq_abnormal_info_pre(struct ft_acq_thread *acq)
{
    struct ft_abnormal *abn = &acq->abn;

    /* 1. 未捕捉信号时，获取异常记录达FT_ABN_GET_CNT_MAX后，应及时处理common info
     * 2. 捕捉信号时，需处理所有信息落入缓存文件
     * */
    if (likely(!ft_get_acq_sig())) {
        if (abn->get_cnt >= FT_ABN_GET_CNT_MAX) {
            abn->get_cnt = 0;
            return FT_FAIL;
        }
    }

    struct ft_file_info *info;
    if (FT_OK != ft_res_entry_get(&abn->res, (void **)&info)) {
        acq->idle++;
        abn->get_cnt = 0;
        return FT_FAIL;
    }
    abn->get_cnt++;

    /* 只要有信息，空闲数清零 */
    acq->idle = 0;

    return __acq_abnormal_info_write(acq, abn, info);
}

static inline void
__acq_truck_init(struct ft_acq_thread *acq, struct ft_acq_truck *truck)
{
    truck->acq = acq;
}

static inline void __acq_file_info_dispose(struct ft_acq_thread *acq,
                                           struct ft_file_info *info)
{
    int ret;
    struct ft_transfer *tsf = acq->tsf[info->type];
    struct ft_acq_truck truck;

    /* 上报平台无任何服务器信息时，无效。落入异常处理文件 */
    if (unlikely(!tsf->cfg.platform[info->attr.platform].srv_cnt)) {
        FT_DEBUG("tsf %s: plat %u srv_cnt is none\n", tsf->name,
                info->attr.platform);
        __acq_abnormal_info_write(acq, &acq->abn, info);
        return;
    }

    __acq_truck_init(acq, &truck);

    /* 初始化对应文件关注信息 */
    if (unlikely(FT_FAIL == ft_acq_file_centre_init(tsf, info))) {
        FT_ERROR("ft_acq_file_centre_init err\n");
        ft_file_info_obj_put(info);
        return;
    }

    if (tsf->hook[FT_FILE_ACQ_PRE]) {
        ret = tsf->hook[FT_FILE_ACQ_PRE](tsf, info, &truck);
        if (unlikely(ret != FT_OK)) {
            FT_ERROR("info %s(fc): hook acq_pre err\n", ft_get_info_path(info));
        }
    }

    if (!info->is_comp) {
        /* 不需压缩，直接入队上传线程 */
        if (FT_OK != ft_file_info_put_upload(acq, info)) {
            __acq_abnormal_info_write(acq, &acq->abn, info);
        }
    } else {
        FT_DEBUG("input compress path = %s\n", ft_get_info_path(info));
        /* 入队压缩线程 */
        if (FT_OK != ft_file_info_put_compress(acq, info)) {
            __acq_abnormal_info_write(acq, &acq->abn, info);
        }
    }

    if (tsf->hook[FT_FILE_ACQ_BACK]) {
        ret = tsf->hook[FT_FILE_ACQ_BACK](tsf, info, &truck);
        if (unlikely(ret != FT_OK)) {
            FT_ERROR("info %s: hook acq_back err\n", ft_get_info_path(info));
        }
    }
}

void *ft_acq_mpmsg_convert(struct ft_mproc_msg *msg)
{
    struct ft_transfer *tsf = g_ft_ctx.tsf[msg->log_type];
    struct ft_info_attr attr = {
        .line = msg->line,
        .sub_type = msg->sub_type,
        .platform = msg->platform,
        .usr_priv = NULL,
    };

    if (tsf->info_priv_sz) {
        attr.usr_priv = ft_file_info_priv_mem_get(g_ft_ctx.acq.public_res,
                msg->log_type);
        if (!attr.usr_priv)
            return NULL;
        memcpy(attr.usr_priv, msg->usr_priv, tsf->info_priv_sz);
    }
    return __ft_info_creat(g_ft_ctx.acq.public_res, msg->path, &attr,
            msg->log_type, FT_INFO_FILE);
}

static void ft_acq_mproc_info_dispose(struct ft_acq_thread *acq)
{
    struct ft_mproc_msg *msg = acq->mp_msg;

    if (!msg)
        return;

    if (!ft_mproc_master_msg_start(msg)) {
        /* 生产未就绪，不做处理 */
        acq->idle++;
        return;
    }

    struct ft_file_info *info = ft_acq_mpmsg_convert(msg);
    if (!info) {
        ft_mproc_master_msg_stop(msg);
        return;
    }
    ft_mproc_master_msg_stop(msg);

    if (unlikely(ft_get_acq_sig())) {
        /* 捕捉到信号时，仅写入文件 */
        __acq_abnormal_info_write(acq, &acq->abn, info);
        return;
    }

    /* 获取有效节点 */
    acq->idle = 0;
    ft_tsf_stat_acq_duty_inc(acq, info->type);
    __acq_file_info_dispose(acq, info);
}

static void ft_acq_common_info_dispose(struct ft_acq_thread *acq)
{
    uint32_t i;
    struct ft_file_info *info;

    for (i = 0; i < acq->duty_cnt; i++) {
        if (FT_OK != ft_res_entry_get(&acq->duty_res[i], (void**)&info)) {
            acq->idle++;
            continue;
        }
        if (unlikely(ft_get_acq_sig())) {
            /* 捕捉到信号时，仅写入文件 */
            __acq_abnormal_info_write(acq, &acq->abn, info);
            continue;
        }

        acq->idle = 0;
        //FT_DEBUG("get path = %s\n", ft_get_info_path(info));
        ft_tsf_stat_acq_duty_inc(acq, info->type);
        __acq_file_info_dispose(acq, info);
    }
}

static int __acq_abnormal_info_convert(struct ft_acq_thread *acq,
                                       struct ft_abnormal *abn,
                                       char *buf,
                                       time_t cur_time)
{
    uint32_t type, flag;
    char *p;
    struct ft_info_attr attr;

    /* 分割字符串为前半部分和后半部分: 通用部分|私有数据部分 */
    //char *common_part = strtok(buf, FT_INFO_PRIV_SPLIT);
    //char *priv_part = strtok(NULL, FT_INFO_PRIV_SPLIT);
    char *priv_part = buf;
    char *common_part = __ft_next_field(&priv_part, '|');
    char *cur = common_part;

    /* tsf type */
    //p = strtok(common_part, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p))
        return FT_FAIL;
    if (!__ft_safe_strtol(p, &type))
        return FT_FAIL;

    /* check */
    if (!ft_tsf_valid_check(type)) {
        return FT_FAIL;
    }

    /* up platform */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p))
        return FT_FAIL;
    if (!__ft_safe_strtol(p, &flag))
        return FT_FAIL;
    attr.platform = flag;

    /* file subtype */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p))
        return FT_FAIL;
    if (!__ft_safe_strtol(p, &flag))
        return FT_FAIL;
    attr.sub_type = flag;

    /* file log line */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p))
        return FT_FAIL;
    if (!__ft_safe_strtol(p, &attr.line))
        return FT_FAIL;

    /* file flag(dir/file/list) */
    //p = strtok(NULL, ",");
    p = __ft_next_field(&cur, ',');
    if (unlikely(!p))
        return FT_FAIL;
    if (!__ft_safe_strtol(p, &flag))
        return FT_FAIL;

    struct ft_file_info *info;
    if (flag != COMPRESS_INPUT_LIST) {
        /* 非list */
        //p = strtok(NULL, ",");
        p = __ft_next_field(&cur, ',');
        if (unlikely(!p))
            return FT_FAIL;
        if (0 != access(p, F_OK)) {
            /* 处理异常文件已不存在 */
            return FT_FAIL;
        }
        attr.usr_priv = NULL;
        info = ft_info_path_precond(&abn->res, p, &attr, type, flag);
        if (!info)
            return FT_FAIL;
    } else {
        info = ft_file_list_creat(type, &abn->res);
        if (!info)
            return FT_FAIL;
        do {
            /* path */
            //p = strtok(NULL, ",");
            p = __ft_next_field(&cur, ',');
            if (!p)
                break;
            /* 异常缓存加载的文件列表，允许不完整 */
            ft_file_list_add(info, p, &abn->res, 0);
        } while (1);

        /* 文件列表预处理，若无fc，立即释放 */
        attr.usr_priv = NULL;
        if (!ft_info_list_precond(info, &attr))
            return FT_FAIL;
    }

    /* 异常文件超时则备份或删除，否则重新处理 */
    if (unlikely(1 == ft_abn_file_or_dir_ops(info, cur_time))) {
        ft_file_info_obj_put(info);
    } else {
        if (priv_part) {
            /* 用户私有数据反序列化 */
            struct ft_transfer *tsf = acq->tsf[type];
            if (!tsf->deser) {
                ft_file_info_obj_put(info);
                FT_ERROR("tsf %s deser func is empty\n", tsf->name);
                return FT_FAIL;
            }

            attr.usr_priv = ft_file_info_priv_mem_get(&abn->res, type);
            if (!attr.usr_priv) {
                ft_file_info_obj_put(info);
                FT_ERROR("tsf %s deser: usr_priv get err\n", tsf->name);
                return FT_FAIL;
            }

            if (FT_OK != tsf->deser(attr.usr_priv, priv_part, strlen(priv_part))) {
                ft_file_info_obj_put(info);
                FT_ERROR("tsf %s deser func fail!(priv_part = %s)\n",
                        tsf->name, priv_part);
                return FT_FAIL;
            }
        }
        ft_file_info_further_init(&attr, info);
        abn->burst_info[abn->burst_cnt++] = info;
    }

    return FT_OK;
}

static void ft_acq_abnormal_info_dispose(struct ft_acq_thread *acq)
{
//#define FT_REWRITE_MIN_LINES 1000
#define FT_REWRITE_PERCENT 40
    uint32_t len;
    uint32_t line = 0;
    //char buf[FT_MAX_LEN_8192];
    struct ft_abnormal *abn = &acq->abn;

    if (unlikely(ft_get_acq_sig())) {
        /* 捕捉到信号时，文件信息不做处理 */
        return;
    }

    if (!abn->fp) {
        /* 无异常记录文件时，不做任何处理 */
        acq->idle++;
        return;
    }

    struct timespec tm;

    clock_gettime(CLOCK_REALTIME_COARSE, &tm);
    acq->idle = 0;

    /* 从最新位置开始处理 */
    abn->burst_cnt = 0;
    fseek(abn->fp, abn->off, SEEK_SET);

    /* FT_ABN_GET_CNT_MAX同时限制burst_info数 */
    char *buf = NULL;
    size_t buf_sz = 0;
    //while (fgets(buf, sizeof(buf), abn->fp) != NULL && line < FT_ABN_GET_CNT_MAX) {
    while (getline(&buf, &buf_sz, abn->fp) != -1 && line < FT_ABN_GET_CNT_MAX) {
        len = strlen(buf);
        buf[len - 1] = 0;   //剥去'\n'
        /* buf转换为ft_file_info */
        __acq_abnormal_info_convert(acq, abn, buf, tm.tv_sec);
        abn->off += len;
        line++;
    }

    if (buf)
        free(buf);  //回收

    abn->info.deal_line += line;
    if (abn->info.deal_line >= abn->info.total_line) {
        /* 当前文件处理完成 */
        fclose(abn->fp);
        abn->fp = NULL;
        abn->info.total_line = abn->info.deal_line = abn->off = 0;
    } else if ((abn->info.deal_line * 100 / abn->info.total_line > FT_REWRITE_PERCENT)) {
        /* 处理函数到达1000行或处理比例到30%时，重写文件, 避免文件持续增大 */
        if (0 == __ft_safe_abn_rewrite_file(abn)) {
            /* 重写成功后更新状态 */
            abn->info.total_line -= abn->info.deal_line;
            abn->info.deal_line = 0;
            abn->off = 0;
        }
    }

    uint32_t i;
    for (i = 0; i < abn->burst_cnt; i++) {
        ft_tsf_stat_acq_abn_inc(acq, abn->burst_info[i]->type);
        /* 入队up/compress */
        __acq_file_info_dispose(acq, abn->burst_info[i]);
    }
}

static inline void ft_acq_sig_abn_detail_dispose(struct ft_abnormal *abn)
{
    if (!ft_task_all_done())
        return;

    //FT_DEBUG("%s\n", __func__);

    /* 无异常文件时或存在时已全部处理，立即结束 */
    if (0 != access(abn->info.file, F_OK)) {
        exit(1);
    } else if (abn->info.deal_line == abn->info.total_line) {
        remove(abn->info.file);
        exit(1);
    } else {
        /* 信号处理过程中，若所有任务已完成，落入当前异常缓存文件处理细节信息 */
        int fd;
        char buf[FT_MAX_LEN_1024];

        /* 使用低级文件IO */
        if ((fd = open(abn->detail_file, O_WRONLY|O_CREAT|O_TRUNC, 0644)) < 0) {
            FT_ERROR("%s open err\n", abn->detail_file);
            exit(1);
        }

        /* 写入关键状态信息 */
        size_t len = snprintf(buf, sizeof(buf), "%lu,%lu,%lu", abn->off,
                abn->info.deal_line, abn->info.total_line);
        if (__sig_safe_write(fd, buf, len) < 0) {
            close(fd);
            exit(1);
        }

        /* 确保数据落盘 */
        fsync(fd);
        close(fd);
        exit(1);
    }
}

void *ft_acquire_process(void *arg)
{
#define FT_ACQ_IDLE_CNT_MAX     1000
    struct ft_acq_thread *acq = arg;

    /* 设置亲和性/线程名 */
    if (FT_OK != ft_thread_priv_attr_set(acq->bind_core, acq->name))
        FT_EXIT(-1);

    /* 等待comp/up线程资源准备完毕 */
    while (!g_ft_ctx.comp_init || !g_ft_ctx.up_init) {
        rte_pause();
    }

    while (1) {
        /* 运维：备份目录超时删除 */
        ft_ops_backup_del(acq);

        /* 异常处理记录信息获取并写入文件缓存 */
        if (FT_OK == ft_acq_abnormal_info_pre(acq))
            continue;

        /* note: 服务开启时立即处理，否则仅落入缓存文件 */
        if (likely(FT_FILETRANS_START == ft_status_get())) {
            /* 正常file_info获取及其处理 */
            ft_acq_common_info_dispose(acq);
            /* 由异常记录文件获取及其处理 */
            ft_acq_abnormal_info_dispose(acq);
            /* 由多进程共享内存获取file_info */
            ft_acq_mproc_info_dispose(acq);
        }

        if (acq->idle >= FT_ACQ_IDLE_CNT_MAX) {
            acq->idle = 0;
            if (unlikely(ft_get_acq_sig())) {
                ft_acq_sig_abn_detail_dispose(&acq->abn);
            } else if (!acq->bind_core) {
                /* 未绑定核心时，若空闲次数达到一定次数，线程资源让出 */
                usleep(1000);
            }
        }
    }

    return NULL;
}

static int ft_thread_acquire_early_init(struct ft_ctx *ctx)
{
    struct ft_cfg *cfg = &ctx->cfg;
    struct ft_acq_thread *acq = &ctx->acq;

    acq->bind_core = cfg->acquire_core;
    acq->tsf = ctx->tsf;
    snprintf(acq->name, sizeof(acq->name), FT_ACQ_THREAD_NAME);

    /* 注册线程公用资源，统筹管理 */
    acq->public_res = ft_acquire_res_creat_public(0, 0, "any_thread_to_acq");
    if (!acq->public_res)
        return FT_FAIL;

    /* acq ops 运维初始化 */
    acq->ops.timer_fd = -1;
    acq->ops.cfg = &ctx->cfg.ops;
    acq->ops.lock = &ctx->ops_lock;
    pthread_rwlock_init(&ctx->ops_lock, NULL);
    if (FT_OK != ft_ops_reset(&ctx->cfg.ops))
        return FT_FAIL;

    /* 异常处理上下文资源初始化 */
    if (FT_OK != ft_acq_abnormal_init(&acq->abn, cfg))
        return FT_FAIL;

    /* 开启多进程模式，创建共享内存 */
    if (cfg->mproc) {
        acq->mp_msg = ft_mproc_master_init();
        if (!acq->mp_msg)
            return FT_FAIL;
    }

    return FT_OK;
}

int ft_thread_acquire_creat(struct ft_ctx *ctx)
{
    int ret;
    struct ft_acq_thread *acq = &ctx->acq;

    /* 线程早期资源初始化 */
    ret = ft_thread_acquire_early_init(ctx);
    FT_ERR_CHK_RET(ret);

    pthread_t tid;
    ret = pthread_create(&tid, NULL, ft_acquire_process, acq);
    if (ret < 0) {
        FT_ERROR("filetrans acquire thread create error\n");
        return FT_FAIL;
    } else {
        FT_INFO("filetrans acquire thread create\n");
        pthread_detach(tid);
    }

    return FT_OK;
}

int ft_acq_priv_set(ft_priv_thread_f priv_fn, void *arg, int idx)
{
    if (idx >= FT_ACQ_PRIV_MAX)
        return FT_FAIL;

    struct ft_acq_thread *acq = &g_ft_ctx.acq;
    struct ft_cfg *cfg = &g_ft_ctx.cfg;

    acq->priv[idx] = priv_fn(arg, cfg->acquire_core);
    if (!acq->priv[idx])
        return FT_FAIL;

    return FT_OK;
}

void *ft_acq_priv_get(void *acq, int idx)
{
    return ((struct ft_acq_thread*)acq)->priv[idx];
}

