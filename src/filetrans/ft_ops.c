#include <dirent.h>
#include <stdarg.h>
#include <sys/wait.h>
#include <sys/timerfd.h>
#include "ft.h"

/* *
 * 依据文件夹类型获取上报平台类型
 *
 * @return
 *  0-合法，-1-非法
 */
static int __ops_cb_arg_plat_set(struct ft_ops_cb_arg *arg,
                                 struct ft_tsf_cfg *cfg,
                                 const char *plat_str)
{
    int i;
    struct ft_up_platform *plat;

    for (i = 0; i < cfg->plat_cnt; i++) {
        plat = &cfg->platform[cfg->plat_idx[i]];
        if (!strcmp(plat_str, plat->name)) {
            arg->plat = plat->type;
            return 0;
        }
    }
    arg->plat = -1;
    return -1;
}

static int __ops_cb_arg_sub_type_set(struct ft_ops_cb_arg *arg,
                                     struct ft_tsf_cfg *cfg,
                                     const char *sub_str)
{
    int i;

    for (i = 0; i < FT_FILE_SUB_TYPE_MAX; i++) {
        if (!strcmp(sub_str, cfg->sub[i].str)) {
            arg->sub_type = i;
            return 0;
        }
    }
    return -1;
}

static int ft_ops_cb_arg_set(struct ft_ops_cb_arg *arg, const char *dir)
{
    if (unlikely(!arg))
        return -1;

    struct ft_transfer *tsf = g_ft_ctx.tsf[arg->tsf_type];
    struct ft_tsf_cfg *cfg = &tsf->cfg;

    /* tsf:
     * 1. 如果关心子类型，优先解析子类型; 非子类型，则尝试解析平台类型
     * 2. 如果不关心子类型，尝试解析平台类型
     * */
    if (cfg->concern_sub) {
        if (!strncmp(dir, FT_OPS_BACK_SUB_DIR, FT_OPS_BACK_SUB_DIR_LEN)) {
            /* subtype类型 */
            arg->sub_type = atoi(dir + FT_OPS_BACK_SUB_DIR_LEN);
            return 0;
        } else {
            if (!__ops_cb_arg_sub_type_set(arg, cfg, dir))
                return 0;

            return __ops_cb_arg_plat_set(arg, cfg, dir);
        }
    } else {
        return __ops_cb_arg_plat_set(arg, cfg, dir);
    }
}

static void __ft_dir_remove(int parent_fd,
                            const char *name,
                            const char *parent_path,
                            struct ft_ops_cb_arg *cb_arg,
                            ft_ops_f ops_fn)
{
    char full_path_str[FT_MAX_LEN_1024];
    char *full_path = full_path_str;

    if (parent_path) {
        if (parent_path == name) {
            full_path = (char*)parent_path;
        } else {
            snprintf(full_path_str, sizeof(full_path_str), "%s/%s", parent_path, name);
        }
    }

    int dir_fd = openat(parent_fd, name, O_RDONLY | O_DIRECTORY | O_NOFOLLOW);
    if (dir_fd == -1) {
        /* 不是目录，尝试删除文件 */
        if (unlinkat(parent_fd, name, 0) == -1) {
            FT_ERROR("Error removing file %s(%s)\n", name, strerror(errno));
#if 0
        /* 以下这种(非dir)，不处理回调*/
        } else {
            /* 全路径 && 有回调函数 && 可以获取有效平台 */
            if (parent_path && ops_fn && !ft_ops_cb_arg_set(cb_arg, name)) {
                ops_fn((const struct ft_ops_cb_arg*)cb_arg, full_path, FT_OPS_TYPE_DEL);
            }
#endif
        }
        return;
    }

    DIR *dir = fdopendir(dir_fd);
    if (!dir) {
        close(dir_fd);
        FT_ERROR("Error opening directory %s(%s)\n", name, strerror(errno));
        return;
    }

    /* 子目录 */
    struct dirent *entry;
    while ((entry = readdir(dir))) {
        if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, ".."))
            continue;

        struct stat filestat;
        if (fstatat(dir_fd, entry->d_name, &filestat, AT_SYMLINK_NOFOLLOW) == -1) {
            FT_ERROR("Error stating %s(%s)\n", entry->d_name, strerror(errno));
            continue;
        }

        if (S_ISDIR(filestat.st_mode)) {
            /* 全路径 && 可以获取有效平台 */
            if (parent_path && !ft_ops_cb_arg_set(cb_arg, entry->d_name)) {
                __ft_dir_remove(dir_fd, entry->d_name, full_path, cb_arg, ops_fn);
            } else {
                __ft_dir_remove(dir_fd, entry->d_name, NULL, cb_arg, ops_fn);
            }
        } else {
            char child_full_path[1536];
            if (parent_path) {
                snprintf(child_full_path, sizeof(child_full_path), "%s/%s",
                        full_path, entry->d_name);
            }
            if (unlinkat(dir_fd, entry->d_name, 0) == -1) {
                FT_ERROR("Error removing %s(%s)\n", entry->d_name, strerror(errno));
            } else {
                if (parent_path && ops_fn) {
                    ops_fn((const struct ft_ops_cb_arg*)cb_arg, child_full_path,
                            FT_OPS_TYPE_DEL);
                }
            }
        }
    }
    closedir(dir);

    /* 删除当前目录 */
    if (unlinkat(parent_fd, name, AT_REMOVEDIR) == -1) {
        FT_ERROR("Error removing dir %s(%s)\n", name, strerror(errno));
    }
}

/* 普通删除 */
void ft_dir_remove(const char *dir)
{
    if (0 != access(dir, F_OK)) {
        FT_ERROR("remove %s is not exist\n", dir);
        return;
    }
    __ft_dir_remove(AT_FDCWD, dir, NULL, NULL, NULL);
}

/* 事件型删除机制：外部可感知 */
static void ft_dir_remove_ops(struct ft_ops_cb_arg *cb_arg, const char *dir)
{
    if (0 != access(dir, F_OK)) {
        FT_ERROR("remove %s is not exist\n", dir);
        return;
    }
    __ft_dir_remove(AT_FDCWD, dir, dir, cb_arg, g_ft_ctx.cfg.ops_fn);
}

int ft_sys_execl(int wait_cld, const char *pro_name, ...)
{
#define FT_SYS_EXECL_MAX       11
    int argc = 0;
    pid_t cld_pid = 0;
    char *argv[FT_SYS_EXECL_MAX] = {0};
    va_list args;

    va_start(args, pro_name);
    for (argc = 0; argc < FT_SYS_EXECL_MAX; argc++) {
        argv[argc] = va_arg(args, char *);
        if (NULL == argv[argc])
            break;
    }
    va_end(args);

    if ((cld_pid = vfork()) < 0) {
        FT_ERROR("Vfork failed\n");
        return FT_FAIL;
    } else if (0 == cld_pid) {
        /* child */
        switch (argc) {
        case 0:
            execl(pro_name, pro_name, NULL);
            break;
        case 1:
            execl(pro_name, pro_name, argv[0], NULL);
            break;
        case 2:
            execl(pro_name, pro_name, argv[0], argv[1], NULL);
            break;
        case 3:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], NULL);
            break;
        case 4:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], NULL);
            break;
        case 5:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], NULL);
            break;
        case 6:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], NULL);
            break;
        case 7:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], argv[6],
                  NULL);
            break;
        case 8:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], argv[6],
                  argv[7], NULL);
            break;
        case 9:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], argv[6],
                  argv[7], argv[8], NULL);
            break;
        case 10:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], argv[6],
                  argv[7], argv[8], argv[9], NULL);
            break;
        case 11:
            execl(pro_name, pro_name, argv[0], argv[1], argv[2], argv[3], argv[4], argv[5], argv[6],
                  argv[7], argv[8], argv[9], argv[10], NULL);
            break;
        }

        /* 若执行成功不会走到这里 */
        FT_ERROR("Execl %s failed, %s\n", pro_name, strerror(errno));
        exit(-1);
    } else {
        /* father */
        if (FT_SYS_WAIT_CLD == wait_cld)
            waitpid(cld_pid, NULL, 0);
    }

    return FT_OK;
#undef FT_SYS_EXECL_MAX
}

/* 判断定时器是否到期: 0-未到期，1-到期 */
static int ft_ops_timer_expire(struct ft_acq_ops *ops)
{
    if (unlikely(ops->timer_fd < 0))
        return 0;

    uint64_t exp = 0;
    int recv_len = read(ops->timer_fd, &exp, sizeof(uint64_t));
    if (recv_len < 0)
        return 0;
    return 1;
}

static void ft_ops_timer_stop(struct ft_acq_ops *ops)
{
    if (unlikely(ops->timer_fd < 0))
        return;
    struct itimerspec value;
    value.it_value.tv_sec = 0;
    value.it_value.tv_nsec = 0;
    value.it_interval.tv_sec = 0;
    value.it_interval.tv_nsec = 0;
    if (timerfd_settime(ops->timer_fd, 0, &value, NULL) == -1) {
        FT_ERROR("ft ops timer stop fail\n");
    }
}

static int ft_ops_timer_init(struct ft_ops_cfg *cfg, struct ft_acq_ops *ops)
{
    /* 创建定时器 */
    if (unlikely(ops->timer_fd < 0)) {
        ops->timer_fd = timerfd_create(CLOCK_REALTIME, TFD_NONBLOCK);
        if (ops->timer_fd < 0) {
            FT_ERROR("filetrans ops timer create fail!\n");
            return FT_FAIL;
        }
    }

    /* 配置检查 */
    if (!cfg->day_time[0]) {
        FT_ERROR("Error! day_time:%s\n", cfg->day_time);
        return FT_FAIL;
    }

    struct tm param_time;
    if (!strptime(cfg->day_time, "%H:%M", &param_time)) {
        FT_ERROR("filetrans ops day time parse err! (%s)\n", cfg->day_time);
        return FT_FAIL;
    }

    time_t target_sec;
    struct timespec tm;
    struct tm target_time;

    /* 获取当前时间并重置目标时间 */
    memset(&target_time, 0, sizeof(target_time));
    clock_gettime(CLOCK_REALTIME_COARSE, &tm);
    ft_second_to_date(tm.tv_sec, &target_time, 8);
    target_time.tm_hour = param_time.tm_hour;
    target_time.tm_min = param_time.tm_min;
    target_time.tm_sec = 0;
    target_sec = mktime(&target_time);


    /* 检查当前时间若即将超过目标时间，则设定为第二天时刻(预留30s) */
    if (target_sec < (tm.tv_sec + 30)) {
        target_time.tm_mday += 1;
        target_sec = mktime(&target_time);  // 更新tm结构以处理月份和年份的变化
    }

    struct itimerspec value;
    value.it_value.tv_sec = target_sec;    // 设置第一次触发时间
    value.it_value.tv_nsec = 0;
    value.it_interval.tv_sec = FT_OPS_ONE_DAY_SEC;   // 每天触发一次
    value.it_interval.tv_nsec = 0;

    if (timerfd_settime(ops->timer_fd, TFD_TIMER_ABSTIME, &value, NULL) == -1) {
        FT_ERROR("ft ops timer set fail\n");
        return FT_FAIL;
    }

    return FT_OK;
}

int ft_ops_reset(struct ft_ops_cfg *cfg)
{
    struct ft_acq_thread *acq = &g_ft_ctx.acq;
    struct ft_acq_ops *ops = &acq->ops;

    if (cfg->nor_valid != FT_OPS_TYPE_BACKUP && cfg->abn_valid != FT_OPS_TYPE_BACKUP) {
        /* 都未开启备份时，关闭定时器 */
        ft_ops_timer_stop(ops);
        if (cfg->abn_valid == FT_OPS_TYPE_DEL) {
            /* 异常超时删除需关注异常超时时间 */
            if (!cfg->abn_back_sec) {
                FT_ERROR("abn_back_sec is %lu\n", cfg->abn_back_sec);
                return FT_FAIL;
            }
        }
        return FT_OK;
    }

    /* 创建filetrans文件备份主目录 */
    if (cfg->nor_valid == FT_OPS_TYPE_BACKUP) {
        if (!cfg->nor_back_path[0] || FT_OK != ft_mkdir(cfg->nor_back_path))
            return FT_FAIL;
    }

    if (cfg->abn_valid == FT_OPS_TYPE_BACKUP) {
        /* 异常超时备份需关注异常超时时间 */
        if (!cfg->abn_back_sec) {
            FT_ERROR("abn_back_sec is %lu\n", cfg->abn_back_sec);
            return FT_FAIL;
        }
        if (!cfg->abn_back_path[0] || FT_OK != ft_mkdir(cfg->abn_back_path))
            return FT_FAIL;
    }

    /* 初始化定时器 */
    if (FT_OK != ft_ops_timer_init(cfg, ops)) {
        return FT_FAIL;
    }

    return FT_OK;
}

int ft_ops_cfg_update(struct ft_ops_cfg *new_cfg)
{
    int ret;
    struct ft_ctx *ctx = &g_ft_ctx;

    pthread_rwlock_wrlock(&ctx->ops_lock);
    memcpy(&ctx->cfg.ops, new_cfg, sizeof(struct ft_ops_cfg));
    ret = ft_ops_reset(&ctx->cfg.ops);
    pthread_rwlock_unlock(&ctx->ops_lock);

    return ret;
}

static int ft_ops_backup_dir_creat(struct ft_ops_cfg *cfg,
                                   struct ft_file_info *info,
                                   char *dir_path,
                                   uint32_t len,
                                   time_t sec,
                                   uint8_t is_abn)
{
    struct tm time_info;
    struct ft_transfer *tsf = g_ft_ctx.tsf[info->type];
    struct ft_up_platform *plat = &tsf->cfg.platform[info->attr.platform];

    ft_second_to_date(sec, &time_info, 8);
    if (!FT_OPS_NEW_INFO_IS(info)) {
        /* nor/abn处理外部交由filetrans的文件 */
        uint32_t tmp_len = snprintf(dir_path, len, "%s/%s/%04d%02d%02d/%s",
                is_abn ? cfg->abn_back_path : cfg->nor_back_path, tsf->name,
                time_info.tm_year + 1900, time_info.tm_mon + 1, time_info.tm_mday,
                plat->name);

        if (tsf->cfg.concern_sub) {
            /* 关心子类型 */
            char *sub_str = NULL;
            if (info->attr.sub_type < FT_FILE_SUB_TYPE_MAX) {
                if (tsf->cfg.sub[info->attr.sub_type].str[0])
                    sub_str = tsf->cfg.sub[info->attr.sub_type].str;
            }
            if (sub_str) {
                snprintf(dir_path + tmp_len, len - tmp_len, "/%s", sub_str);
            } else {
                snprintf(dir_path + tmp_len, len - tmp_len, "/%s%u",
                        FT_OPS_BACK_SUB_DIR, info->attr.sub_type);
            }
        }
    } else {
        /* 仅涉及nor处理filetrans自生成的文件 */
        snprintf(dir_path, len, "%s/%s/%04d%02d%02d/%s/%s",
                cfg->nor_back_path, tsf->name,
                time_info.tm_year + 1900, time_info.tm_mon + 1, time_info.tm_mday,
                plat->name, FT_OPS_BACK_NEW_DIR);
    }

    return ft_mkdir(dir_path);
}

void ft_file_or_dir_backup(char *file, char *back_dir)
{
    if (0 != access(file, F_OK)) {
        FT_ERROR("backup %s is not exist\n", file);
        return;
    }

    if (0 > ft_sys_execl(FT_SYS_WAIT_CLD, "/bin/mv", file, back_dir, NULL)) {
        FT_ERROR("Failed to Back up %s\n", file);
    }
}

void ft_comp_ori_file_del(struct ft_file_info *info)
{
    struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;

    if (info->icc_hdr.type == COMPRESS_INPUT_DIR) {
        /* 删除原始文件夹 */
        ft_dir_remove(icc->ipi.path);
    } else {
        /* 删除原始文件列表 */
        while (icc) {
            remove(icc->ipi.path);
            icc = icc->next;
        }
    }
}

void ft_comp_ori_file_backup(struct ft_file_info *info,
                             char *back_dir,
                             uint8_t is_back_ori)
{
    struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;

    if (info->icc_hdr.type == COMPRESS_INPUT_DIR) {
        /* 依据配置决定原始文件夹是备份或删除 */
        if (is_back_ori) {
            ft_file_or_dir_backup(icc->ipi.path, back_dir);
        } else {
            ft_dir_remove(icc->ipi.path);
        }
    } else {
        /* 依据配置决定原始文件列表是备份或删除 */
        while (icc) {
            if (is_back_ori) {
                ft_file_or_dir_backup(icc->ipi.path, back_dir);
            } else {
                remove(icc->ipi.path);
            }
            icc = icc->next;
        }
    }
}

static int ft_abn_file_or_dir_del(struct ft_ops_cfg *cfg,
                                  struct ft_file_info *info,
                                  time_t cur_sec)
{
    struct stat filestat;

    if (info->is_comp) {
        /* 异常文件，在前面异常时，已删除压缩文件，
         * 故这里只删除原始文件
         * */
        struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
        stat(icc->ipi.path, &filestat);
        if (unlikely((filestat.st_mtime + cfg->abn_back_sec) < cur_sec)) {
            ft_comp_ori_file_del(info);
            return 1;
        }
    } else {
        /* 非压缩类型文件超时删除：
         *  即删除原始文件
         * */
        struct ft_file_centre *fc = &info->fc;
        stat(fc->path, &filestat);
        if (unlikely((filestat.st_mtime + cfg->abn_back_sec) < cur_sec)) {
            if (info->fc_type != COMPRESS_INPUT_DIR) {
                remove(fc->path);
            } else {
                ft_dir_remove(fc->path);
            }
            return 1;
        }
    }

    return 0;
}


static int ft_abn_file_or_dir_backup(struct ft_ops_cfg *cfg,
                                     struct ft_file_info *info,
                                     time_t cur_sec)
{
    char dir_path[FT_MAX_LEN_512];
    struct stat filestat;

    if (info->is_comp) {
#if 1
        /* 异常文件，在前面异常时，已删除压缩文件，
         * 故这里只备份原始文件
         * */
        struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;

        stat(icc->ipi.path, &filestat);
        if (unlikely((filestat.st_mtime + cfg->abn_back_sec) < cur_sec)) {
            if (FT_OK != ft_ops_backup_dir_creat(cfg, info, dir_path,
                        sizeof(dir_path), cur_sec, 1)) {
                return 0;
            }
            ft_comp_ori_file_backup(info, dir_path, 1);
            return 1;
        }
#else
        char comp_name[FT_MAX_LEN_256];
        ft_outfile_local_name_creat(info, comp_name, sizeof(comp_name));
        stat(comp_name, &filestat);
        /* 压缩类型文件超时备份:
         *  1. 压缩文件存在必备份
         *  2. 压缩前原始文件/列表/目录，依据配置备份或删除
         * */
        if (unlikely((filestat.st_mtime + cfg->abn_back_sec) < cur_sec)) {
            if (FT_OK != ft_ops_backup_dir_creat(cfg, info, dir_path,
                        sizeof(dir_path), cur_sec, 1)) {
                return 0;
            }
            ft_file_or_dir_backup(comp_name, dir_path);
            ft_comp_ori_file_backup(info, dir_path, cfg->is_back_ori);
            return 1;
        }
#endif
    } else {
        /* 非压缩类型文件超时备份：
         *  即备份原始文件
         * */
        struct ft_file_centre *fc = &info->fc;
        stat(fc->path, &filestat);
        if (unlikely((filestat.st_mtime + cfg->abn_back_sec) < cur_sec)) {
            if (FT_OK != ft_ops_backup_dir_creat(cfg, info, dir_path,
                        sizeof(dir_path), cur_sec, 1)) {
                return 0;
            }
            ft_file_or_dir_backup(fc->path, dir_path);
            return 1;
        }
    }

    return 0;
}

static int ft_nor_file_or_dir_backup(struct ft_ops_cfg *cfg,
                                     struct ft_file_info *info,
                                     time_t cur_sec)
{
    char dir_path[FT_MAX_LEN_512];

    if (info->is_comp) {
        char comp_name[FT_MAX_LEN_256];
        ft_outfile_local_name_creat(info, comp_name, sizeof(comp_name));
        /* 压缩类型文件超时备份:
         *  1. 压缩文件存在必备份
         *  2. 压缩前原始文件/列表/目录，依据配置备份或删除
         * */
        if (FT_OK != ft_ops_backup_dir_creat(cfg, info, dir_path,
                    sizeof(dir_path), cur_sec, 0)) {
            return 0;
        }
        ft_file_or_dir_backup(comp_name, dir_path);
        ft_comp_ori_file_backup(info, dir_path, cfg->is_back_ori);
        return 1;
    } else {
        /* 非压缩类型文件超时备份：
         *  即备份原始文件
         * */
        struct ft_file_centre *fc = &info->fc;
        if (FT_OK != ft_ops_backup_dir_creat(cfg, info, dir_path,
                    sizeof(dir_path), cur_sec, 0)) {
            return 0;
        }
        ft_file_or_dir_backup(fc->path, dir_path);
        return 1;
    }

    return 0;
}

static int ft_nor_file_or_dir_del(struct ft_ops_cfg *cfg, struct ft_file_info *info)
{
    if (info->is_comp) {
        char comp_name[FT_MAX_LEN_256];
        ft_outfile_local_name_creat(info, comp_name, sizeof(comp_name));
        /* 压缩类型文件及其原文件删除 */
        remove(comp_name);
        ft_comp_ori_file_del(info);
        return 1;
    } else {
        /* 非压缩类型文件删除 */
        struct ft_file_centre *fc = &info->fc;
        if (info->fc_type != COMPRESS_INPUT_DIR) {
            remove(fc->path);
        } else {
            ft_dir_remove(fc->path);
        }

        return 1;
    }
}

/* 备份或删除正常上报文件，返回值：0-未处理，1-备份或删除成功*/
int ft_nor_file_or_dir_ops(struct ft_file_info *info, time_t cur_sec)
{
    struct ft_ctx *ctx = &g_ft_ctx;
    struct ft_ops_cfg *cfg = &ctx->cfg.ops;

    if (FT_OPS_TYPE_NONE == cfg->nor_valid)
        return 0;

    int ret;

    pthread_rwlock_rdlock(&ctx->ops_lock);
    if (FT_OPS_TYPE_BACKUP == cfg->nor_valid) {
        /* 备份 */
        ret = ft_nor_file_or_dir_backup(cfg, info, cur_sec);
    } else {
        /* 删除 */
        ret = ft_nor_file_or_dir_del(cfg, info);
    }
    pthread_rwlock_unlock(&ctx->ops_lock);

    return ret;
}

/* 备份或删除超时异常文件，返回值：0-未处理，1-备份或删除成功*/
int ft_abn_file_or_dir_ops(struct ft_file_info *info, time_t cur_sec)
{
    struct ft_ctx *ctx = &g_ft_ctx;
    struct ft_ops_cfg *cfg = &ctx->cfg.ops;

    if (FT_OPS_TYPE_NONE == cfg->abn_valid) {
        return 0;
    }

    int ret;
    pthread_rwlock_rdlock(&ctx->ops_lock);
    if (FT_OPS_TYPE_BACKUP == cfg->abn_valid) {
        /* 超时备份 */
        ret = ft_abn_file_or_dir_backup(&ctx->cfg.ops, info, cur_sec);
    } else {
        /* 超时删除 */
        ret = ft_abn_file_or_dir_del(&ctx->cfg.ops, info, cur_sec);
    }
    pthread_rwlock_unlock(&ctx->ops_lock);

    return ret;
}

static int ft_ops_is_older_dir(const char *date_str, uint32_t del_back_day, time_t cur)
{
    struct tm date;

    /* 解析日期字符串 */
    if (!strptime(date_str, "%Y%m%d", &date)) {
        FT_ERROR("filetrans dir %s time parse err!\n", date_str);
        return 0;
    }
    date.tm_hour = date.tm_min = date.tm_sec = 0;

    /* 转换为时间戳 */
    time_t date_time = mktime(&date);

    /* 计算时间差 */
    double diff = difftime(cur, date_time);
    return diff >= (del_back_day * FT_OPS_ONE_DAY_SEC); //超过del_back_day天返回1
}

void ft_ops_tsf_dir_chk_and_clean(struct ft_ops_cb_arg *cb_arg,
                                  const char *tsf_path,
                                  uint32_t del_back_day)
{
    DIR *dir = opendir(tsf_path);
    if (!dir) {
        FT_ERROR("Error opening directory %s\n", tsf_path);
        return;
    }

    char date_path[FT_MAX_LEN_512];
    struct dirent *entry;
    while ((entry = readdir(dir))) {
        if (DT_DIR != entry->d_type)
            continue;
        /* 检查是否是日期文件夹（格式为 YYYYMMDD，长度8）*/
        if (strlen(entry->d_name) != 8)
            continue;
        if (ft_ops_is_older_dir(entry->d_name, del_back_day, cb_arg->ops_sec)) {
            /* 删除超时的日期文件夹 */
            snprintf(date_path, sizeof(date_path), "%s/%s", tsf_path, entry->d_name);
            ft_dir_remove_ops(cb_arg, date_path);
        }
    }
    closedir(dir);
}

static void ft_ops_backup_dir_del(void *acq, char *backup_path, uint32_t del_back_day)
{
    DIR *dir = opendir(backup_path);
    if (!dir) {
        FT_ERROR("Error opening directory %s\n", backup_path);
        return;
    }

    int tsf_type;
    char tsf_path[FT_MAX_LEN_512];
    struct dirent *entry;
    struct timespec tm;
    struct ft_ops_cb_arg cb_arg;

    clock_gettime(CLOCK_REALTIME_COARSE, &tm);
    /* cb_arg初始化 */
    cb_arg.acq = acq;
    cb_arg.ops_sec = tm.tv_sec;
    cb_arg.plat = -1;

    while ((entry = readdir(dir))) {
        if (DT_DIR != entry->d_type)
            continue;
        if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, ".."))
            continue;
        tsf_type = ft_transfer_type_get(entry->d_name);
        if (-1 == tsf_type)
            continue;
        cb_arg.sub_type = -1;
        cb_arg.tsf_type = tsf_type;
        snprintf(tsf_path, sizeof(tsf_path), "%s/%s", backup_path, entry->d_name);
        /* 判断备份的tsf目录下的日期文件夹是否超时，超时即删除 */
        ft_ops_tsf_dir_chk_and_clean(&cb_arg, tsf_path, del_back_day);
    }
    closedir(dir);
}

void ft_ops_backup_del(struct ft_acq_thread *acq)
{
    struct ft_acq_ops *ops = &acq->ops;

    /* normal/abnormal均未开启备份，立即返回 */
    if (ops->cfg->nor_valid != FT_OPS_TYPE_BACKUP &&
            ops->cfg->abn_valid != FT_OPS_TYPE_BACKUP)
        return;

    pthread_rwlock_rdlock(ops->lock);
    if (ft_ops_timer_expire(ops)) {
        /* 定时器已到期，开始删除备份目录 */
        if (ops->cfg->nor_valid == FT_OPS_TYPE_BACKUP) {  //normal
            ft_ops_backup_dir_del(acq, ops->cfg->nor_back_path, ops->cfg->del_back_day);
        }
        if (ops->cfg->abn_valid == FT_OPS_TYPE_BACKUP) {  //abnormal
            ft_ops_backup_dir_del(acq, ops->cfg->abn_back_path, ops->cfg->del_back_day);
        }
    }
    pthread_rwlock_unlock(ops->lock);
}

