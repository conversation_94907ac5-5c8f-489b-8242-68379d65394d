#ifndef __FT_H__
#define __FT_H__

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <libdeflate.h>
#include <grp.h>
#include <pwd.h>
#include <zlib.h>
#include <fcntl.h>
#include <stdint.h>
#include <sys/param.h>
#include <sys/types.h>
#include <sys/mman.h>
#include <sys/sysmacros.h>
#include <tar.h>
#include <rte_errno.h>
#include <rte_ring.h>
#include <rte_mempool.h>
#include "ft_stat.h"

#include "filetrans.h"
#include "ft_ops.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define __ft_packed      __attribute__((__packed__))

/** 统一返回值检查 */
#define FT_ERR_CHK_RET(ret)                 \
    do {                                    \
        if ((ret) != FT_OK) {return (ret);} \
    } while (0)

#define FT_ALIGN(x, align)      (((x) + (align) - 1) & ~((align) - 1))

/** 页大小向上取整 */
#define FT_PGUP(x, pgsz)        FT_ALIGN(x, pgsz)

#define FT_MAX(a, b)       ((a) >= (b) ? (a) : (b))
#define FT_MIN(a, b)       ((a) <= (b) ? (a) : (b))

/** 文件操作 */
#define FT_READ_FLAG        (O_RDONLY | O_NONBLOCK | O_NOCTTY | O_NOFOLLOW)
#define FT_WRITE_FLAG       (O_RDWR | O_CREAT | O_NONBLOCK | O_NOCTTY | O_NOFOLLOW)
#define FT_DIR_PERMISSION   ACCESSPERMS

/** format buf */
#define FT_FORMAT_BUF(dst, max, use, fmt, ...)                              \
    do {                                                                    \
        int __tmp_len;                                                      \
        if (max > use) {                                                    \
            __tmp_len = snprintf(dst + use, max - use, fmt, ##__VA_ARGS__); \
            if (__tmp_len > 0) {                                            \
                use += __tmp_len;                                           \
            }                                                               \
        }                                                                   \
    } while (0)

/** filetrans自身日志接口 */
#define FT_COLOR_NONE    "\033[0m"
#define FT_RED           "\033[0;31m"
#define FT_BLUE          "\033[0;33m"

#define FT_PRINTF_BLUE(fmt, ...)                                \
        printf(FT_BLUE "%s >[%s:%d]# " fmt FT_COLOR_NONE,       \
                "filetrans", __func__, __LINE__, ##__VA_ARGS__);

#define FT_PRINTF_RED(fmt, ...)                                 \
        printf(FT_RED "%s >[%s:%d]# " fmt FT_COLOR_NONE,        \
                "filetrans", __func__, __LINE__, ##__VA_ARGS__);

#ifdef DEBUG
#define FT_DEBUG(fmt, ...)                                      \
    do {                                                        \
        FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                      \
        if (g_ft_ctx.cfg.log_fn)                                \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_DEBUG], fmt, ##__VA_ARGS__);  \
    } while (0)

#define FT_INFO(fmt, ...)                                       \
    do {                                                        \
        FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                      \
        if (g_ft_ctx.cfg.log_fn)                                \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_INFO], fmt, ##__VA_ARGS__);   \
    } while (0)

#define FT_NOTICE(fmt, ...)                                     \
    do {                                                        \
        FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                      \
        if (g_ft_ctx.cfg.log_fn)                                \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_NOTICE], fmt, ##__VA_ARGS__); \
    } while (0)

#define FT_ERROR(fmt, ...)                                      \
    do {                                                        \
        FT_PRINTF_RED(fmt, ##__VA_ARGS__)                       \
        if (g_ft_ctx.cfg.log_fn)                                \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_ERR], fmt, ##__VA_ARGS__);    \
    } while (0)
#else
#define FT_DEBUG(fmt, ...)                                      \
    do {                                                        \
        if (g_ft_ctx.cfg.log_fn) {                              \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_DEBUG], fmt, ##__VA_ARGS__);  \
        } else {                                                \
            FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                  \
        }                                                       \
    } while (0)

#define FT_INFO(fmt, ...)                                       \
    do {                                                        \
        if (g_ft_ctx.cfg.log_fn) {                              \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_INFO], fmt, ##__VA_ARGS__);   \
        } else {                                                \
            FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                  \
        }                                                       \
    } while (0)

#define FT_NOTICE(fmt, ...)                                     \
    do {                                                        \
        if (g_ft_ctx.cfg.log_fn) {                              \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_NOTICE], fmt, ##__VA_ARGS__); \
        } else {                                                \
            FT_PRINTF_BLUE(fmt, ##__VA_ARGS__)                  \
        }                                                       \
    } while (0)

#define FT_ERROR(fmt, ...)                                      \
    do {                                                        \
        if (g_ft_ctx.cfg.log_fn) {                              \
            g_ft_ctx.cfg.log_fn(g_ft_ctx.cfg.log_level          \
                    [FT_LOG_LEVEL_ERR], fmt, ##__VA_ARGS__);    \
        } else {                                                \
            FT_PRINTF_RED(fmt, ##__VA_ARGS__)                   \
        }                                                       \
    } while (0)
#endif  // DEBUG

#define FT_EXIT(code)      (exit(code))

#define FT_INPUT_DIR        COMPRESS_INPUT_DIR      //非压缩目录上传复用参数

struct ft_comp_thread;
struct ft_up_thread;

struct ft_duty_tsf_stat {
    uint64_t expect_ori_cnt;    /**< 作为外部传入资源时，处理的预期原始文件数 */
    uint64_t priv_get_err;      /**< tsf priv池：内存获取失败计数 */
};

/**
 * 基于不同的文件输入责任线程的统计.
 */
struct ft_duty_stat {
    struct ft_duty_tsf_stat tsf[FT_TRANSFER_TYPE_MAX];
    uint64_t en_err;                     /**< 入队失败计数 */
    uint64_t en_ok;                      /**< 入队成功计数 */
    uint64_t de_ok;                      /**< 出队成功计数 */

    uint64_t pool_get_err;               /**< 内存获取失败计数 */
    uint64_t pool_get_ok;                /**< 内存获取成功计数 */
    uint64_t pool_put;                   /**< 内存释放计数 */

    uint64_t icc_pool_get_err;           /**< 内存获取失败计数 */
    uint64_t icc_pool_get_ok;            /**< 内存获取成功计数 */
    uint64_t icc_pool_put;               /**< 内存释放计数 */
};

/**
 * 线程对接责任线程资源.
 */
struct ft_duty_res {
    char src_name[FT_MAX_LEN_64];                    /**< 责任线程名(输入) */
    char dst_name[FT_MAX_LEN_64];                    /**< 负责线程名(输出) */
    struct rte_mempool *pool;                        /**< acq/abn关注 */
    struct rte_mempool *icc_pool;                    /**< acq/abn关注 */
    struct rte_ring *ring;
    struct ft_duty_stat stat;                        /**< 责任线程统计信息 */
};

typedef int (*ft_m_up_data_f)(struct ft_up_thread *up,
                              const struct ft_up_srv *srv,
                              struct ft_file_info *info,
                              char *url);
typedef int (*ft_m_up_chg_name_f)(CURL *curl,
                                  const struct ft_up_srv *srv,
                                  char *old_name,
                                  char *new_name);
/**
 * 不同上报模式对应操作集
 */
struct ft_mode_up {
    ft_m_up_data_f up_data;                     /**< 上报接口 */
    ft_m_up_chg_name_f change_name;             /**< 更名接口 */
};

/**
 * 异常处理上下文资源结构
 */
struct ft_abnormal {
#define FT_ABN_GET_CNT_MAX      16
#define FT_ABN_INFO_RECORD      "abnormal_info"     /* type,path */
#define FT_ABN_DETAIL_RECORD    ".abn_detail"       /* off,deal_line,total_line */
    FILE *fp;                                   /**< 异常记录文件指针 */
    char detail_file[FT_MAX_LEN_512];           /**< 异常记录文件处理细节文件名 */
    uint32_t get_cnt;                           /**< 异常处理由队列获取数 */
    uint32_t burst_cnt;                         /**< 由异常记录文件中加载的info数 */
    uint64_t off;                               /**< 当前文件已处理偏移 */
    struct ft_duty_res res;                     /**< 处理异常线程资源 */
    struct ft_file_info *burst_info[FT_ABN_GET_CNT_MAX];
                                                /**< 异常记录文件加载info集 */
    struct ft_abnormal_info info;               /**< 异常记录文件信息 */
};

/**
 * acquire线程运维信息
 */
struct ft_acq_ops {
    int timer_fd;                       /**< 定时器：定时检查删除备份超n天文件 */
    struct ft_ops_cfg *cfg;             /**< 运维配置 */
    pthread_rwlock_t *lock;             /**< 用于运维配置更改的读写锁 */
};

struct ft_acq_tsf_stat {
    uint64_t acq_duty_cnt;                  /**< acq通过duty资源获取的文件数 */
    uint64_t acq_abn_cnt;                   /**< acq通过abnormal获取的文件数 */
};

struct ft_comp_tsf_stat {
    uint64_t comp_duty_cnt;                 /**< comp通过duty资源获取的文件数 */
};

struct ft_up_tsf_stat {
    uint64_t up_duty_cnt;                   /**< up通过duty资源获取的文件数 */
    uint64_t up_succeed_cnt;                /**< 上报成功文件数(disp/copy都1次) */
};

/**
 * acquire线程信息
 */
struct ft_acq_thread {
/* 超512M时不对文件做映射 */
#define FT_ACQ_MMAP_FILE_SZ_MAX         (512 * 1024 * 1024)
#define FT_ACQ_THREAD_NAME              "ft_acquire"
#define FT_ACQ_DUTY_CNT_MAX             32
#define FT_ACQ_PRIV_MAX 4               // acquire线程用户私有数据最大数量
    uint8_t sig_flag;                             /**< 0:未捕捉信号，1:捕捉信号 */
    uint16_t bind_core;
    uint16_t duty_cnt;                            /**< 责任线程数 */
    uint32_t disp_base_up;                        /**< 分发up线程基数 */
    uint32_t disp_base_comp;                      /**< 分发comp线程基数 */
    uint32_t idle;                                /**< 记录线程的空闲数 */
    char name[FT_MAX_LEN_64];                     /**< 线程名 */
    struct ft_transfer **tsf;                     /**< 责任transfer集 */
    struct ft_duty_res duty_res[FT_ACQ_DUTY_CNT_MAX];
                                                  /**< 责任线程资源 */
    struct ft_acq_tsf_stat tsf_stat[FT_TRANSFER_TYPE_MAX];
                                                  /**< acq处理统计 */
    struct ft_duty_res *public_res;               /**< 公用线程资源，被duty_res接管 */
    struct ft_abnormal abn;                       /**< 异常处理 */
    struct ft_acq_ops ops;                        /**< acq线程运维信息 */
    struct ft_mproc_msg *mp_msg;                  /**< 多进程共享消息 */
    void *priv[FT_ACQ_PRIV_MAX];                  /**< 线程用户私有数据资源 */
};

/**
 * 通用统计结构
 */
struct ft_performance_stat {
    struct ft_stat_slice window[FT_STAT_WINDOW_SIZE];  /**< 环形缓冲区数据 */
    volatile uint8_t current_idx;   /**< 当前写入索引 */
    volatile time_t last_update;    /**< 最后更新时间 */
    enum ft_stat_metric_type type;  /**< 统计指标类型 */
};

/**
 * 上传线程统计数据
 */
struct ft_upload_stat {
    struct ft_performance_stat perf_stat;  /**< 通用性能统计 */
    volatile size_t total_bytes;           /**< 累计上传字节数 */
    volatile uint64_t total_files;         /**< 累计上传文件数 */
};

/**
 * 压缩线程统计数据
 */
struct ft_compress_stat {
    struct ft_performance_stat perf_stat;  /**< 通用性能统计 */
    volatile size_t total_input_bytes;     /**< 累计输入字节数 */
    volatile size_t total_output_bytes;    /**< 累计输出字节数 */
    volatile uint64_t total_files;         /**< 累计处理文件数 */
};

/**
 * 全局统计管理器
 */
struct ft_stat_manager {
    struct ft_performance_stat upload_stats[FT_THREAD_MAX];
    struct ft_compress_stat compress_stats[FT_THREAD_MAX];
    uint8_t upload_count;
    uint8_t compress_count;
};

/**
 * compress线程信息
 */
struct ft_comp_thread {
    uint8_t th_idx;                               /**< 线程索引id */
    uint16_t bind_core;
    char name[FT_MAX_LEN_64];                     /**< 线程名 */
    void *compressor;                             /**< 压缩器 */
    struct ft_transfer **tsf;                     /**< 责任transfer集 */
    struct ft_duty_res acq_res;                   /**< 责任acq线程资源 */
    struct ft_duty_res self_res;                  /**< 自身线程资源 */
    struct ft_comp_tsf_stat tsf_stat[FT_TRANSFER_TYPE_MAX];
                                                  /**< comp处理统计 */
    struct ft_compress_stat stat;                 /**< 压缩性能统计 */
};

struct ft_up_disp_base {
    uint16_t num[FT_UP_PLATFORM_CNT_MAX];
                                        /**< disp方式选择服务器的基数 */
};





/**
 * upload线程信息
 */
struct ft_up_thread {
#define FT_UP_TRY_CNT_MAX   3
#define FT_UP_PRIV_MAX 4               // upload线程用户私有数据最大数量
    uint8_t sig_flag;                             /**< 0:未捕捉信号，1:捕捉信号 */
    uint8_t done;                                 /**< 0:未上报完成，1:全上报完成 */
    uint8_t th_idx;                               /**< 线程索引id */
    uint8_t duty_comp_cnt;                        /**< 责任comp线程数 */
    uint16_t bind_core;
    uint32_t idle;                                /**< 记录线程的空闲数 */
    char name[FT_MAX_LEN_64];                     /**< 线程名 */
    CURL *curl;                                   /**< curl上报器 */
    struct ft_transfer **tsf;                     /**< 责任transfer集 */
    struct ft_up_disp_base disp[FT_TRANSFER_TYPE_MAX];
                                                  /**< 分发模式的基数，每tsf一份 */
    struct ft_up_tsf_stat tsf_stat[FT_TRANSFER_TYPE_MAX];
                                                  /**< upload处理统计 */
    struct ft_mode_up *mu;                        /**< 各模式上报上下文结构 */
    struct ft_duty_res acq_res;                   /**< 责任acq线程资源 */
    struct ft_duty_res *comp_res[FT_THREAD_MAX];  /**< 责任comp线程资源 */
    struct ft_upload_stat stat;                   /**< 上传性能统计 */
    void *priv[FT_UP_PRIV_MAX];                   /**< 线程用户私有数据资源 */
};

/**
 * filetrans全局上下文主结构
 */
struct ft_ctx {
    uint8_t status;                                  /**< 0:停止服务，1:开启服务(默认) */
    uint8_t comp_enable;                             /**< 是否开启压缩线程，取决tsf */
    uint8_t comp_init;                               /**< comp线程早期初始化完成标志 */
    uint8_t up_init;                                 /**< up线程早期初始化完成标志 */
    uint16_t tsf_cnt;                                /**< 有效tsf数 */
    uint64_t pgsz;                                   /**< 系统页大小 */
    uint16_t tsf_idx[FT_TRANSFER_TYPE_MAX];          /**< 有效tsf索引 */
    pthread_rwlock_t ops_lock;                       /**< 用于运维配置更改的读写锁 */
    struct ft_cfg cfg;                               /**< 配置信息 */
    struct ft_acq_thread acq;                        /**< 文件获取线程 信息 */
    struct ft_comp_thread comp[FT_THREAD_MAX];       /**< 压缩线程 信息 */
    struct ft_up_thread up[FT_THREAD_MAX];           /**< 上报线程 信息 */
    struct ft_transfer *tsf[FT_TRANSFER_TYPE_MAX];   /**< 支持的所有类型文件传输对象 */
    struct ft_mode_up mu[FT_UPLOAD_MAX];             /**< 各模式上报上下文结构 */
};

struct ft_info_attr_priv_hdr {
#define FT_INFO_ATTR_PRIV_HDR_SZ    offsetof(struct ft_info_attr_priv_hdr, data)
    uint16_t tsf_type;
    void *pool;
    char data[];
};

extern struct ft_ctx g_ft_ctx;
/* ft_malloc 和 ft_free 现在由 hmemory.h 提供 */

#define FT_SYS_PGSZ     (g_ft_ctx.pgsz)

/**
 * 线程属性设置，如亲和性/线程名.
 *
 * @param bind_core
 *  亲和核.
 * @param name
 *  线程名.
 * @return
 *  FT_OK: 设置成功.
 *  FT_FAIL: 设置失败.
 */
int ft_thread_priv_attr_set(uint16_t bind_core, char *name);

/**
 * 设置线程资源对应源/目的线程名.
 *
 * @param res
 *  线程资源.
 * @param name
 *  线程名.
 * @param is_src
 *  1:源线程，0:目的线程
 * @return
 *  FT_OK: 设置成功.
 *  FT_FAIL: 设置失败.
 */
void ft_thread_res_name_set(struct ft_duty_res *res, char *name, int is_src);

/**
 * 入队entry至线程资源队列
 *
 * @param res
 *  线程资源.
 * @param entry
 *  待入队项.
 * @param en_abn
 *  1:线程资源入队失败尝试再入队abnormal队列
 *  0:不尝试入队给abnormal队列
 * @return
 *  FT_OK: 入队成功.
 *  FT_FAIL: 入队失败.
 */
int ft_res_entry_put(struct ft_duty_res *res, void *entry, uint8_t en_abn);

/**
 * 处理异常文件信息至abnormal队列
 *
 * @param info
 *  文件信息.
 * @return
 *  FT_OK: 入队成功.
 *  FT_FAIL: 入队失败.
 */
int ft_abnormal_info_put(struct ft_file_info *info);
int ft_res_entry_get(struct ft_duty_res *res, void **entry);

void ft_comp_icc_obj_put(struct comp_input_centre *icc, struct ft_duty_res *res);
void *ft_comp_icc_obj_get(struct ft_duty_res *res);

void ft_file_info_obj_put(struct ft_file_info *info);
void *ft_file_info_obj_get(struct ft_duty_res *res, uint32_t log_type, uint8_t input_type);

int ft_mkdir(const char *dir);

void *ft_file_mem_mmap(int fd, size_t sz);

uint8_t ft_task_all_done(void);

void ft_tsf_srv_seg_url_creat(struct ft_up_srv *srv);
char *ft_get_info_path(struct ft_file_info *info);

void ft_file_centre_init(struct ft_file_centre *fc);
uint8_t ft_get_fi_comp_type(uint8_t log_type);

int ft_second_to_date(const time_t sec, struct tm *tm, int time_zone);

int ft_transfer_type_get(char *tsf_name);

void ft_file_info_priv_mem_put(void *usr_priv);

/* 删除已压缩成功的文件 */
void ft_comp_file_remove(struct ft_file_info *info);

int file_info_path_parse(struct ft_file_centre *fc, char *path);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FT_H__ */
