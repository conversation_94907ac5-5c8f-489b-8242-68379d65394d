#ifndef __FT_STAT_H__
#define __FT_STAT_H__

#include <stdint.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* 滑动窗口统计配置 */
#define FT_STAT_WINDOW_SIZE     10      /**< 滑动窗口大小（秒数） */
#define FT_STAT_TIME_SLICE      1       /**< 时间片大小（秒） */

/* 统计指标类型 */
enum ft_stat_metric_type {
    FT_STAT_METRIC_UPLOAD = 0,      /**< 上传指标 */
    FT_STAT_METRIC_COMPRESS,        /**< 压缩指标 */
    FT_STAT_METRIC_MAX
};

/* 通用时间片统计数据 */
struct ft_stat_slice {
    volatile time_t timestamp;      /**< 时间片时间戳 */
    volatile size_t bytes;          /**< 该时间片内处理字节数 */
    volatile uint64_t files;        /**< 该时间片内处理文件数 */
    volatile double ratio;          /**< 该时间片内平均比率（压缩比等） */
};

/* 通用统计结构 */
struct ft_performance_stat {
    struct ft_stat_slice window[FT_STAT_WINDOW_SIZE];  /**< 环形缓冲区数据 */
    volatile uint8_t current_idx;   /**< 当前写入索引 */
    volatile time_t last_update;    /**< 最后更新时间 */
    enum ft_stat_metric_type type;  /**< 统计指标类型 */
};

/* 压缩线程统计数据 */
struct ft_compress_stat {
    struct ft_performance_stat perf_stat;  /**< 通用性能统计 */
    volatile size_t total_input_bytes;     /**< 累计输入字节数 */
    volatile size_t total_output_bytes;    /**< 累计输出字节数 */
    volatile uint64_t total_files;         /**< 累计处理文件数 */
};

/**
 * 通用统计初始化
 *
 * @param stat
 *  统计数据结构指针
 * @param type
 *  统计指标类型
 * @return
 *  0: 初始化成功
 *  -1: 初始化失败
 */
int ft_stat_init(struct ft_performance_stat *stat, enum ft_stat_metric_type type);

/**
 * 通用统计更新
 *
 * @param stat
 *  统计数据结构指针
 * @param bytes
 *  本次处理的字节数
 * @param files
 *  本次处理的文件数
 * @param ratio
 *  本次处理的比率（压缩比等，上传模块可传0）
 * @return
 *  0: 更新成功
 *  -1: 更新失败
 */
int ft_stat_update(struct ft_performance_stat *stat, size_t bytes, 
                   uint64_t files, double ratio);

/**
 * 通用统计查询
 *
 * @param stat
 *  统计数据结构指针
 * @param rate_KBps
 *  输出参数：处理速率（KB/s）
 * @param fps
 *  输出参数：文件处理速率（files/s）
 * @param avg_ratio
 *  输出参数：平均比率（压缩比等，上传模块为0）
 * @return
 *  0: 查询成功
 *  -1: 查询失败
 */
int ft_stat_get(struct ft_performance_stat *stat, double *rate_KBps, 
                double *fps, double *avg_ratio);

/**
 * 压缩统计初始化
 *
 * @param comp_stat
 *  压缩统计数据结构指针
 * @return
 *  0: 初始化成功
 *  -1: 初始化失败
 */
int ft_comp_stat_init(struct ft_compress_stat *comp_stat);

/**
 * 压缩统计更新
 *
 * @param comp_stat
 *  压缩统计数据结构指针
 * @param input_bytes
 *  输入数据字节数
 * @param output_bytes
 *  输出数据字节数
 * @param files
 *  处理的文件数
 * @return
 *  0: 更新成功
 *  -1: 更新失败
 */
int ft_comp_stat_update(struct ft_compress_stat *comp_stat, size_t input_bytes, 
                        size_t output_bytes, uint64_t files);

/**
 * 压缩统计查询
 *
 * @param comp_stat
 *  压缩统计数据结构指针
 * @param compress_rate_KBps
 *  输出参数：压缩速率（KB/s，基于输入数据）
 * @param fps
 *  输出参数：文件处理速率（files/s）
 * @param compression_ratio
 *  输出参数：平均压缩比率
 * @return
 *  0: 查询成功
 *  -1: 查询失败
 */
int ft_comp_stat_get(struct ft_compress_stat *comp_stat, double *compress_rate_KBps, 
                     double *fps, double *compression_ratio);

/* 线程索引管理的统计接口（用于全局统计管理） */

/**
 * 通用统计初始化（基于线程索引）
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @return
 *  0: 初始化成功
 *  -1: 初始化失败
 */
int ft_stat_init_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx);

/**
 * 通用统计更新（基于线程索引）
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @param bytes
 *  本次处理的字节数
 * @param files
 *  本次处理的文件数
 * @param ratio
 *  本次处理的比率（压缩比等，上传模块可传0）
 * @return
 *  0: 更新成功
 *  -1: 更新失败
 */
int ft_stat_update_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx, 
                          size_t bytes, uint64_t files, double ratio);

/**
 * 通用统计查询（基于线程索引）
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @param rate_KBps
 *  输出参数：处理速率（KB/s）
 * @param fps
 *  输出参数：文件处理速率（files/s）
 * @param avg_ratio
 *  输出参数：平均比率（压缩比等，上传模块为0）
 * @return
 *  0: 查询成功
 *  -1: 查询失败
 */
int ft_stat_get_by_idx(enum ft_stat_metric_type type, uint8_t thread_idx,
                       double *rate_KBps, double *fps, double *avg_ratio);

/**
 * 压缩统计初始化（基于线程索引）
 *
 * @param comp_idx
 *  压缩线程索引
 * @return
 *  0: 初始化成功
 *  -1: 初始化失败
 */
int ft_comp_stat_init_by_idx(uint8_t comp_idx);

/**
 * 压缩统计更新（基于线程索引）
 *
 * @param comp_idx
 *  压缩线程索引
 * @param input_bytes
 *  输入数据字节数
 * @param output_bytes
 *  输出数据字节数
 * @param files
 *  处理的文件数
 * @return
 *  0: 更新成功
 *  -1: 更新失败
 */
int ft_comp_stat_update_by_idx(uint8_t comp_idx, size_t input_bytes, 
                               size_t output_bytes, uint64_t files);

/**
 * 压缩统计查询（基于线程索引）
 *
 * @param comp_idx
 *  压缩线程索引
 * @param compress_rate_KBps
 *  输出参数：压缩速率（KB/s，基于输入数据）
 * @param fps
 *  输出参数：文件处理速率（files/s）
 * @param compression_ratio
 *  输出参数：平均压缩比率
 * @return
 *  0: 查询成功
 *  -1: 查询失败
 */
int ft_comp_stat_get_by_idx(uint8_t comp_idx, double *compress_rate_KBps, 
                            double *fps, double *compression_ratio);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FT_STAT_H__ */