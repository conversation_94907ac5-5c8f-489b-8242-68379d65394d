#ifndef __FT_STAT_H__
#define __FT_STAT_H__

#include <stdint.h>
#include <time.h>
#include <sys/types.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */


/* 滑动窗口统计配置 */
#define FT_STAT_WINDOW_SIZE     10      /**< 滑动窗口大小（秒数） */
#define FT_STAT_TIME_SLICE      1       /**< 时间片大小（秒） */

/* 环形缓冲区时间片统计数据 */
struct ft_stat_slice {
    volatile time_t timestamp;          /**< 时间片时间戳 */
    volatile size_t bytes;              /**< 该时间片内处理字节数 */
    volatile uint64_t files;            /**< 该时间片内处理文件数 */
    volatile double ratio;              /**< 该时间片内平均比率（压缩比等） */
};

/**
 * 统计指标类型
 */
enum ft_stat_metric_type {
    FT_STAT_METRIC_UPLOAD = 0,      /**< 上传指标 */
    FT_STAT_METRIC_COMPRESS,        /**< 压缩指标 */
    FT_STAT_METRIC_MAX
};

/**
 * 初始化统计数据结构
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_stat_init(enum ft_stat_metric_type type, uint8_t thread_idx);

/**
 * 更新统计数据
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @param bytes
 *  处理字节数
 * @param files
 *  处理文件数
 * @param ratio
 *  比率（压缩比等）
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_stat_update(enum ft_stat_metric_type type, uint8_t thread_idx,
                   size_t bytes, uint64_t files, double ratio);

/**
 * 获取统计数据
 *
 * @param type
 *  统计指标类型
 * @param thread_idx
 *  线程索引
 * @param rate
 *  处理速率（字节/秒）
 * @param fps
 *  文件处理速率（文件/秒）
 * @param avg_ratio
 *  平均比率
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_stat_get(enum ft_stat_metric_type type, uint8_t thread_idx,
                double *rate, double *fps, double *avg_ratio);

/**
 * 初始化压缩统计数据
 *
 * @param comp_idx
 *  压缩线程索引
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_comp_stat_init(uint8_t comp_idx);

/**
 * 更新压缩统计数据
 *
 * @param comp_idx
 *  压缩线程索引
 * @param input_bytes
 *  输入字节数
 * @param output_bytes
 *  输出字节数
 * @param files
 *  处理文件数
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_comp_stat_update(uint8_t comp_idx, size_t input_bytes,
                        size_t output_bytes, uint64_t files);

/**
 * 获取压缩统计数据
 *
 * @param comp_idx
 *  压缩线程索引
 * @param compress_rate_KBps
 *  压缩速率（KB/s，基于输入数据）
 * @param fps
 *  文件处理速率（文件/秒）
 * @param compression_ratio
 *  压缩比率（输入大小/输出大小）
 * @return
 *  0: 成功
 *  -1: 失败
 */
int ft_comp_stat_get(uint8_t comp_idx, double *compress_rate_KBps,
                     double *fps, double *compression_ratio);
/**
 * 初始化上传统计数据（兼容接口）
 *
 * @param up_idx
 *  上传线程索引
 */
void ft_up_stat_init(uint8_t up_idx);

/**
 * 更新上传统计数据（兼容接口）
 *
 * @param up_idx
 *  上传线程索引
 * @param bytes
 *  上传字节数
 * @param files
 *  上传文件数
 */
void ft_up_stat_update(uint8_t up_idx, size_t bytes, uint64_t files);

/**
 * 获取上传统计数据（兼容接口）
 *
 * @param KBps
 *  上传速率（KB/s）
 * @param fps
 *  文件上传速率（文件/秒）
 * @param up_idx
 *  上传线程索引
 */
void ft_up_stat_get(double *KBps, double *fps, uint8_t up_idx);

/**
 * 获取统计管理器实例
 *
 * @return
 *  统计管理器指针
 */
struct ft_stat_manager *ft_stat_manager_get(void);

/**
 * 清空所有统计数据
 */
void ft_stat_clear_all(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __FT_STAT_H__ */
