#ifndef __COMP_H__
#define __COMP_H__

#include <stdio.h>
#include <stdlib.h>
#include <stddef.h>
#include <string.h>
#include <string.h>
#include <sys/param.h>
#include <sys/mman.h>
#include <sys/sysmacros.h>
#include <pwd.h>
#include <grp.h>
#include <fcntl.h>
#include <unistd.h>
#include <time.h>
#include <libdeflate.h>
#include <isa-l/igzip_lib.h>
#include <isa-l/crc.h>

#include "compress.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifndef likely
#define likely(x)     __builtin_expect(!!(x), 1)
#endif

#ifndef unlikely
#define unlikely(x)   __builtin_expect(!!(x), 0)
#endif

#define __comp_packed       __attribute__((__packed__))

#define COMP_MAX(a, b)       ((a) >= (b) ? (a) : (b))

#define COMP_WRITE_FLAG     (O_RDWR | O_CREAT | O_NONBLOCK | O_NOCTTY | O_NOFOLLOW)

#define COMP_OUTPUT_END_PADDING         8
#define COMP_MIN_BLOCK_LENGTH           5000

/* 流模式单次读取buf大小 */
#define COMP_STREAM_BUF_SZ              (8 * 1024 * 1024)

/* 整除向上取整 */
#define COMP_DIV_ROUND_UP(n, d)         (((n) + (d) - 1) / (d))

/** 页大小向上取整 */
#define COMP_PGUP(x, pgsz)              (((x) + (pgsz) - 1) & ~((pgsz) - 1))

/* isal流模式未结束标志 */
#define COMP_ISAL_STREAM_NOT_END(isal)  (isal->internal_state.state != ZSTATE_END)

/* 尾队列安全遍历接口 */
#ifndef TAILQ_FOREACH_SAFE
#define TAILQ_FOREACH_SAFE(var, head, field, tvar) \
    for ((var) = TAILQ_FIRST((head));              \
        (var) && ((tvar) = TAILQ_NEXT((var), field), 1); (var) = (tvar))
#endif

/* deflate压缩级别 */
enum comp_deflate_level {
#define COMP_DEFLATE_COMPRESS_LEVEL_DEF   COMP_DEFLATE_COMPRESS_LEVEL6
    COMP_DEFLATE_COMPRESS_LEVEL0 = 0,
    COMP_DEFLATE_COMPRESS_LEVEL1 = 1,
    COMP_DEFLATE_COMPRESS_LEVEL6 = 6,
    COMP_DEFLATE_COMPRESS_LEVEL9 = 9,
    COMP_DEFLATE_COMPRESS_LEVEL12 = 12
};

/* isal压缩级别 */
enum comp_isal_level {
    COMP_ISAL_COMPRESS_LEVEL0 = 0,
    COMP_ISAL_COMPRESS_LEVEL1 = 1,
    COMP_ISAL_COMPRESS_LEVEL2 = 2,
    COMP_ISAL_COMPRESS_LEVEL3 = 3
};

struct comp_compressor;
typedef int (*comp_init_f)(struct comp_compressor *compressor, enum compress_level level);
typedef size_t (*comp_data_f)(struct comp_compressor *compressor,
                              const char *in,
                              uint32_t src_len,
                              char *out,
                              uint32_t dst_len);
typedef int (*comp_file_f)(struct comp_compressor *compressor,
                           struct comp_input_centre_hdr *icc_hdr,
                           struct comp_output_centre *occ);

/**
 * 压缩器主结构
 */
struct comp_compressor {
#define d_compressor   libdeflate_compressor
#define i_compressor   isal_zstream
    uint8_t type;                                 /**< 压缩器类型 */
    uint8_t level;                                /**< 压缩级别 */
    union {
        struct d_compressor *deflate;             /**< deflate压缩器 */
        struct i_compressor *isal;                /**< isal压缩器 */
    };
    comp_init_f init;                             /**< 初始化 */
    comp_data_f comp_data;                        /**< 压缩数据接口 */
    comp_file_f comp_file[COMPRESS_MODE_MAX];     /**< 压缩文件接口 */
};

int compress_file_stream_file_prepare(struct comp_in_path_info *ipi,
                                      struct comp_output_centre *occ,
                                      FILE **out_fp,
                                      uint8_t **in_buf,
                                      uint8_t **out_buf);

int comp_write_out_file_mmap(struct comp_in_path_info *ipi,
                             struct comp_output_centre *occ);

uint32_t comp_inter_filename_get(struct comp_input_centre *icc,
                                 char **filename);

int comp_input_path_parse(struct comp_in_path_info *ipi,
                          char *path,
                          uint32_t dir_base_len,
                          enum compress_input type);

int compress_file_tgz_isal(struct comp_compressor *compressor,
                           struct comp_input_centre_hdr *icc_hdr,
                           struct comp_output_centre *occ);

int compress_file_tgz_deflate(struct comp_compressor *comp,
                              struct comp_input_centre_hdr *icc_hdr,
                              struct comp_output_centre *occ);

int compress_file_gz_isal(struct comp_compressor *compressor,
                          struct comp_input_centre_hdr *icc_hdr,
                          struct comp_output_centre *occ);
int compress_file_gz_deflate(struct comp_compressor *comp,
                             struct comp_input_centre_hdr *icc_hdr,
                             struct comp_output_centre *occ);

int compress_file_zip_isal(struct comp_compressor *compressor,
                           struct comp_input_centre_hdr *icc_hdr,
                           struct comp_output_centre *occ);
int compress_file_zip_deflate(struct comp_compressor *compressor,
                              struct comp_input_centre_hdr *icc_hdr,
                              struct comp_output_centre *occ);

void *__out_mem_mmap(int fd, size_t sz);

/**
 * 同步压缩文件到本地(压缩基于内存，压缩文件同步到磁盘)
 *
 * @param occ
 *  压缩输出文件核心信息
 * @return
 *  COMP_OK: 备份成功.
 *  COMP_FAIL: 备份失败.
 */
static inline int compress_outfile_sync_local(struct comp_output_centre *occ)
{
    /* 基于文件流的不需同步 */
    if (!occ->buf)
        return COMP_OK;

    /* 将内存映射文件的更改同步到磁盘 */
    if (0 != msync(occ->buf, occ->buf_sz, MS_SYNC)) {
        return COMP_FAIL;
    }

    return COMP_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __COMP_H__ */
