#include "comp.h"

extern size_t comp_zip_isal_bound(struct comp_input_centre *icc);

size_t comp_gz_isal_bound(struct comp_input_centre *icc)
{
    return sizeof(struct isal_gzip_header) + comp_zip_isal_bound(icc);
}

static inline size_t comp_gz_deflate_bound(struct d_compressor *deflate,
                                           struct comp_input_centre *icc)
{
    return libdeflate_gzip_compress_bound(deflate, icc->filestat.st_size);
}

static inline int comp_deflate_gz(struct d_compressor *deflate,
                                  struct comp_input_centre *icc,
                                  struct comp_output_centre *occ)
{
    uint32_t complen = libdeflate_gzip_compress(deflate, icc->buf, icc->filestat.st_size,
            occ->buf, occ->map_sz);
    if (0 == complen)
        return COMP_FAIL;

    /* 实际压缩后长度 */
    occ->buf_sz = complen;
    return COMP_OK;
}

int compress_file_gz_deflate(struct comp_compressor *comp,
                             struct comp_input_centre_hdr *icc_hdr,
                             struct comp_output_centre *occ)
{
    /* deflate方式目前不支持list/dir */
    if (unlikely(icc_hdr->type != COMPRESS_INPUT_FILE || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* 输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_gz_deflate_bound(comp->deflate, icc);

    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_deflate_gz(comp->deflate, icc, occ)) {
            //printf("%s deflate gz err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_deflate_gz(comp->deflate, icc, occ)) {
            //printf("%s deflate gz err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

uint32_t __compress_isal_gz(struct i_compressor *isal,
                            void *in, size_t in_nbytes,
                            void *out, size_t out_nbytes_avail)
{
    struct isal_gzip_header gz_hdr;

    /* gz_hdr init */
    isal_gzip_header_init(&gz_hdr);

    isal_deflate_reset(isal);
    isal->end_of_stream = 1;

    isal->avail_in = in_nbytes;
    isal->next_in = (uint8_t*)in;

    isal->avail_out = out_nbytes_avail;
    isal->next_out = out;

    /* gz_hdr + data */
    isal_write_gzip_header(isal, &gz_hdr);
    isal_deflate(isal);

    if (unlikely(out_nbytes_avail < isal->avail_out))
        return 0;

    uint32_t *p;
    uint32_t off = (out_nbytes_avail - isal->avail_out);

    /* crc32 + insize*/
    p = (uint32_t*)((char*)out + off);
    *p = crc32_gzip_refl(0, (void*)in, in_nbytes);
    off += sizeof(*p);

    p = (uint32_t*)((char*)out + off);
    *p = in_nbytes;
    off += sizeof(*p);

    return off;
}

static inline int comp_isal_gz(struct i_compressor *isal,
                               struct comp_input_centre *icc,
                               struct comp_output_centre *occ)
{
    uint32_t complen = __compress_isal_gz(isal, icc->buf, icc->filestat.st_size,
            occ->buf, occ->map_sz);
    if (0 == complen)
        return COMP_FAIL;

    /* 实际压缩后长度 */
    occ->buf_sz = complen;
    return COMP_OK;
}

static int compress_file_gz_isal_mem(struct comp_compressor *comp,
                                     struct comp_input_centre_hdr *icc_hdr,
                                     struct comp_output_centre *occ)
{
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* 输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_gz_isal_bound(icc);

    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_isal_gz(comp->isal, icc, occ)) {
            //printf("%s isal gz err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        /* 关联文件 */
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_isal_gz(comp->isal, icc, occ)) {
            //printf("%s isal gz err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

static int compress_file_gz_isal_stream(struct comp_compressor *comp,
                                        struct comp_input_centre_hdr *icc_hdr,
                                        struct comp_output_centre *occ)
{
    int ret;
    FILE *in_fp = NULL, *out_fp = NULL;
    uint8_t *in_buf = NULL, *out_buf = NULL;
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;

    /* 压缩准备 */
    in_fp = fopen(icc->ipi.path, "rb");
    if (unlikely(!in_fp)) {
        ret = COMP_FAIL;
        //printf("info %s fopen err\n", icc->ipi.path);
        goto done;
    }

    if (COMP_OK != (ret = compress_file_stream_file_prepare(&icc->ipi, occ, &out_fp,
                    &in_buf, &out_buf))) {
        goto done;
    }

    uint32_t file_sz = 0;
    uint32_t crc32_v = 0;
    struct isal_gzip_header gz_hdr;
    struct i_compressor *isal = comp->isal;

    isal_deflate_reset(isal);

    /* gz_hdr */
    isal->avail_in = 0;
    isal->next_out = out_buf;
    isal->avail_out = COMP_STREAM_BUF_SZ;
    //isal->gzip_flag = IGZIP_GZIP_NO_HDR;
    isal_gzip_header_init(&gz_hdr);
    isal_write_gzip_header(isal, &gz_hdr);
    fwrite(out_buf, 1, isal->total_out, out_fp);
    file_sz += isal->total_out;

    /* data */
    do {
        isal->avail_in = (uint32_t) fread(in_buf, 1, COMP_STREAM_BUF_SZ, in_fp);
        isal->end_of_stream = feof(in_fp) ? 1 : 0;
        isal->next_in = in_buf;
        /* crc32计算 */
        crc32_v = crc32_gzip_refl(crc32_v, in_buf, isal->avail_in);
        do {
            isal->avail_out = COMP_STREAM_BUF_SZ;
            isal->next_out = out_buf;
            isal_deflate(isal);
            fwrite(out_buf, 1, COMP_STREAM_BUF_SZ - isal->avail_out, out_fp);
            file_sz += (COMP_STREAM_BUF_SZ - isal->avail_out);
        } while (isal->avail_out == 0);

        if (unlikely(isal->avail_in != 0)) {
            //printf("info %s isal avail_in not 0 (%u)\n", icc->ipi.path,
            //        isal->avail_in);
            goto done;
        }
    } while (COMP_ISAL_STREAM_NOT_END(isal));

    /* crc32 + insize*/
    fwrite(&crc32_v, 1, sizeof(uint32_t), out_fp);
    fwrite(&icc->filestat.st_size, 1, sizeof(uint32_t), out_fp);
    file_sz += (sizeof(uint32_t) * 2);

    occ->buf_sz = file_sz;
    ret = COMP_OK;

done:
    if (in_fp)
        fclose(in_fp);
    if (out_fp)
        fclose(out_fp);
    if (in_buf)
        free(in_buf);
    if (out_buf)
        free(out_buf);

    return ret;
}

int compress_file_gz_isal(struct comp_compressor *compressor,
                          struct comp_input_centre_hdr *icc_hdr,
                          struct comp_output_centre *occ)
{
    if (unlikely(!icc_hdr || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    /* gzip不支持list/dir */
    if (unlikely(COMPRESS_INPUT_FILE != icc_hdr->type)) {
        //printf("gzip unsupport dir/list(%s)\n", icc_hdr->icc_list_hdr->ipi.path);
        return COMP_FAIL;
    }

    if (icc_hdr->icc_list_hdr->buf) {
        /* 基于内存压缩 */
        //printf("gz compress file %s, isal by mem\n", icc_hdr->icc_list_hdr->ipi.path);
        return compress_file_gz_isal_mem(compressor, icc_hdr, occ);
    } else {
        /* 基于文件流压缩 */
        //printf("gz compress file %s, isal by stream\n",
        //icc_hdr->icc_list_hdr->ipi.path);
        return compress_file_gz_isal_stream(compressor, icc_hdr, occ);
    }
}
