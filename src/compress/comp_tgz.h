#ifndef __COMP_TGZ_H__
#define __COMP_TGZ_H__

#include <tar.h>
#include "comp.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* useful constants */
/* see FIXME note in block.c regarding COMP_T_BLOCKSIZE */
#define COMP_T_BLOCKSIZE      512
#define COMP_T_NAMELEN        100
#define COMP_T_PREFIXLEN      155
#define COMP_T_MAXPATHLEN     (COMP_T_NAMELEN + COMP_T_PREFIXLEN)

#define COMP_T_TVERSION       TVERSION
#define COMP_T_TMAGIC         TMAGIC

/* tar Header Block, from POSIX 1003.1-1990.  */
struct comp_tar_header
{
    char name[100];
    char mode[8];
    char uid[8];
    char gid[8];
    char size[12];
    char mtime[12];
    char chksum[8];
    char typeflag;
    char linkname[100];
    char magic[6];
    char version[2];
    char uname[32];
    char gname[32];
    char devmajor[8];
    char devminor[8];
    char prefix[155];
    char padding[12];
};

struct comp_tar {
    // tartype_t *type;
    // const char *pathname;
    // long fd;
    // int oflags;
    // int options;
    struct comp_tar_header th_buf;
    // libtar_hash_t *h;

    /* introduced in libtar 1.2.21 */
    // char *th_pathname;
};

/* constant values for the TAR options field */
#define COMP_TAR_GNU              1   /* use GNU extensions */
#define COMP_TAR_VERBOSE          2   /* output file info to stdout */
#define COMP_TAR_NOOVERWRITE      4   /* don't overwrite existing files */
#define COMP_TAR_IGNORE_EOT       8   /* ignore double zero blocks as EOF */
#define COMP_TAR_CHECK_MAGIC      16  /* check magic in file header */
#define COMP_TAR_CHECK_VERSION    32  /* check version in file header */
#define COMP_TAR_IGNORE_CRC       64  /* ignore CRC in file header */

/* this is obsolete - it's here for backwards-compatibility only */
#define COMP_TAR_IGNORE_MAGIC    0

/* encode file info in th_header */
#define comp_th_set_mtime(t, fmtime) \
    comp_th_int_to_oct_nonull((fmtime), (t)->th_buf.mtime, 12)
#define comp_th_set_size(t, fsize) \
    comp_th_int_to_oct_nonull((fsize), (t)->th_buf.size, 12)

/* integer to NULL-terminated string-octal conversion */
#define comp_th_int_to_oct(num, oct, octlen) \
    snprintf((oct), (octlen), "%*o ", (octlen) - 2, (num));

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __COMP_TGZ_H__ */
