#include "comp.h"
#include "hmemory.h"

#define COMP_DIR_PERMISSION   ACCESSPERMS

uint64_t g_comp_pgsz;                                   /**< 系统页大小 */

/* 压缩文件名(无后缀) */
static inline uint32_t __comp_comp_filename_get(struct comp_in_path_info *ipi,
                                                struct comp_output_centre *occ,
                                                char **filename)
{
    if (!occ->comp_name) {
        /* 未指定时，选第一个作为压缩文件名 */
        *filename = COMP_IN_FILE_NAME(ipi);
        return ipi->file_len ? ipi->file_len : (ipi->dir_len - 1);
    } else {
        *filename = occ->comp_name;
        return strlen(occ->comp_name);
    }
}

/*
 * @brief 创建目录检测函数.
 */
static int __comp_mkdir(const char *dir, int flag)
{
    int ret;
    int i, len;
    char str[COMP_MAX_LEN_512];

    len = strlen(dir);
    if (len >= sizeof(str))
        return COMP_FAIL;
    memcpy(str, dir, len);
    str[len] = 0;

    for (i = 0; i < len; i++) {
        if ('/' == str[i] && 0 != i) {
            str[i] = '\0';
            if (access(str, F_OK)) {
                ret = mkdir(str, COMP_DIR_PERMISSION);
                if (0 != ret) {
                    return COMP_FAIL;
                }
            }
            str[i] = '/';
        }
    }

    if (flag) {
        if (len > 0 && 0 != access(str, F_OK)) {
            ret = mkdir(str, COMP_DIR_PERMISSION);
            if (0 != ret) {
                return COMP_FAIL;
            }
        }
    }

    return COMP_OK;
}

/*
 * @brief 创建目录函数.
 */
static int comp_mkdir(const char *dir)
{
    int ret;

    if (0 != access(dir, F_OK)) {
        ret = mkdir(dir, COMP_DIR_PERMISSION);
        if (0 != ret && COMP_OK != __comp_mkdir(dir, 1)) {
            return COMP_ERR_MKDIR;
        }
    }

    return COMP_OK;
}

static int compress_creat_f_name_zip(struct comp_in_path_info *ipi,
                                     struct comp_output_centre *occ,
                                     char *comp_name,
                                     size_t dst_len)
{
    size_t len;
    char *name;

    len = __comp_comp_filename_get(ipi, occ, &name);
    if (unlikely(dst_len <= (len + 4))) //4: .zip
        return COMP_PATH_OVER;
    memcpy(comp_name, name, len);
    snprintf(comp_name + len, dst_len - len, ".zip");
    return COMP_OK;
}

static int compress_creat_f_name_gz(struct comp_in_path_info *ipi,
                                    struct comp_output_centre *occ,
                                    char *comp_name,
                                    size_t dst_len)
{
    size_t len;
    char *name;

    len = __comp_comp_filename_get(ipi, occ, &name);
    if (unlikely(dst_len <= (len + 3)))    // 3: .gz
        return COMP_PATH_OVER;
    memcpy(comp_name, name, len);
    snprintf(comp_name + len, dst_len - len, ".gz");
    return COMP_OK;
}

static int compress_creat_f_name_tgz(struct comp_in_path_info *ipi,
                                     struct comp_output_centre *occ,
                                     char *comp_name,
                                     size_t dst_len)
{
    size_t len;
    char *name;

    len = __comp_comp_filename_get(ipi, occ, &name);
    if (unlikely(dst_len <= (len + 7)))    //7: .tar.gz
        return COMP_PATH_OVER;
    memcpy(comp_name, name, len);
    snprintf(comp_name + len, dst_len - len, ".tar.gz");
    return COMP_OK;
}

uint32_t comp_inter_filename_get(struct comp_input_centre *icc,
                                 char **filename)
{
    /* 解压内文件名 */
    if (!icc->inter_name) {
        /* 原文件名 */
        *filename = COMP_IN_FILE_NAME(&icc->ipi);
        return COMP_IN_FILE_NAME_LEN(&icc->ipi);
    } else {
        /* 自定义文件名 */
        *filename = icc->inter_name;
        return strlen(icc->inter_name);
    }
}

static inline int __comp_out_file_name_init(struct comp_in_path_info *ipi,
                                            struct comp_output_centre *occ,
                                            char *file_name,
                                            size_t file_name_len)
{
    /* 获取完整压缩文件路径 */
    int ret;
    switch (occ->mode) {
        case COMPRESS_MODE_ZIP:
            ret = compress_creat_f_name_zip(ipi, occ, file_name, file_name_len);
            break;
        case COMPRESS_MODE_GZIP:
            ret = compress_creat_f_name_gz(ipi, occ, file_name, file_name_len);
            break;
        case COMPRESS_MODE_TGZ:
            ret = compress_creat_f_name_tgz(ipi, occ, file_name, file_name_len);
            break;
        default:
            ret = COMP_FAIL;
    }

    return ret;
}

static int __comp_out_file_name_get(struct comp_in_path_info *ipi,
                                    struct comp_output_centre *occ,
                                    char *file_name,
                                    size_t file_name_len)
{
    if (COMP_OK != comp_mkdir(occ->comp_path)) {
        return COMP_ERR_MKDIR;
    }
    int len = snprintf(file_name, file_name_len, "%s/", occ->comp_path);
    return __comp_out_file_name_init(ipi, occ, file_name + len, file_name_len - len);
}

int compress_out_file_name_init(struct comp_input_centre *icc,
                                struct comp_output_centre *occ,
                                char *file_name,
                                size_t file_name_len,
                                uint8_t concern_local)
{
    size_t len = 0;

    if (concern_local)
        len = snprintf(file_name, file_name_len, "%s/", occ->comp_path);

    return __comp_out_file_name_init(&icc->ipi, occ, file_name + len,
            file_name_len - len);
}

void *__out_mem_mmap(int fd, size_t sz)
{
    int rw_flag = (PROT_READ | PROT_WRITE);
    int map_flag = (MAP_SHARED | MAP_POPULATE);

    if (fd < 0)
        map_flag |= MAP_ANONYMOUS;  //无名映射

    /* 预填充映射的page，避免压缩时的Pagefault(MAP_POPULATE) */
    void *mem = mmap(NULL, COMP_PGUP(sz, g_comp_pgsz), rw_flag, map_flag, fd, 0);

    return (mem == MAP_FAILED) ? NULL : mem;
}

int comp_write_out_file_mmap(struct comp_in_path_info *ipi,
                             struct comp_output_centre *occ)
{
    char comp_file_name[COMP_MAX_LEN_256];

    /* 获取压缩文件名 */
    if (unlikely(COMP_OK != __comp_out_file_name_get(ipi, occ, comp_file_name,
                    sizeof(comp_file_name)))) {
        return -1;
    }

    int fd = open(comp_file_name, COMP_WRITE_FLAG);
    if (0 > fd) {
        return -1;
    }

    if (0 > ftruncate(fd, occ->map_sz)) {
        //printf("%s ftruncate err\n", comp_file_name);
        goto err;
    }

    occ->buf = __out_mem_mmap(fd, occ->map_sz);
    if (!occ->buf) {
        //printf("%s mmap file err\n", comp_file_name);
        goto err;
    }

    return fd;

err:
    close(fd);
    return -1;
}

static inline void __compressor_level_isal_conv(struct comp_compressor *comp,
                                                enum compress_level level)
{
    if (COMPRESS_LEVEL_FASTEST == level) {
        comp->level = COMP_ISAL_COMPRESS_LEVEL0;
    } else if (COMPRESS_LEVEL_MEDIUM == level) {
        comp->level = COMP_ISAL_COMPRESS_LEVEL1;
    } else if (COMPRESS_LEVEL_SLOW == level) {
        comp->level = COMP_ISAL_COMPRESS_LEVEL2;
    } else {
        comp->level = COMP_ISAL_COMPRESS_LEVEL3;
    }
}

static inline void __compressor_level_deflate_conv(struct comp_compressor *comp,
                                                   enum compress_level level)
{
    if (COMPRESS_LEVEL_FASTEST == level) {
        comp->level = COMP_DEFLATE_COMPRESS_LEVEL1;
    } else if (COMPRESS_LEVEL_MEDIUM == level) {
        comp->level = COMP_DEFLATE_COMPRESS_LEVEL6;
    } else if (COMPRESS_LEVEL_SLOW == level) {
        comp->level = COMP_DEFLATE_COMPRESS_LEVEL9;
    } else if (COMPRESS_LEVEL_NONE == level) {
        comp->level = COMP_DEFLATE_COMPRESS_LEVEL0;
    } else {
        comp->level = COMP_DEFLATE_COMPRESS_LEVEL12;
    }
}

static inline void *__compressor_isal_creat(uint8_t level)
{
    struct i_compressor *isal = hmem_malloc(sizeof(struct i_compressor));
    if (!isal)
        return NULL;

    /* isal内部初始化 */
    isal_deflate_init(isal);

    isal->level = level;
    if (COMP_ISAL_COMPRESS_LEVEL1 == isal->level) {
        isal->level_buf_size = ISAL_DEF_LVL1_DEFAULT;
    } else if (COMP_ISAL_COMPRESS_LEVEL2 == isal->level) {
        isal->level_buf_size = ISAL_DEF_LVL2_DEFAULT;
    } else if (COMP_ISAL_COMPRESS_LEVEL3 == isal->level) {
        isal->level_buf_size = ISAL_DEF_LVL3_DEFAULT;
    } else {
        /* level0 不需要level_buf */
        return isal;
    }

    isal->level_buf = hmem_malloc(isal->level_buf_size);
    if (!isal->level_buf) {
        hmem_free(isal);
        return NULL;
    }

    return isal;
}

static inline void *__compressor_deflate_creat(uint8_t level)
{
    return libdeflate_alloc_compressor(level);
}

static int compressor_isal_init(struct comp_compressor *compressor,
                                enum compress_level level)
{
    /* 转为内部level，创建核心压缩器 */
    __compressor_level_isal_conv(compressor, level);
    compressor->isal = __compressor_isal_creat(compressor->level);
    if (!compressor->isal)
        return COMP_FAIL;

    return COMP_OK;
}

static int compressor_deflate_init(struct comp_compressor *compressor,
                                   enum compress_level level)
{
    /* 转为内部level，创建核心压缩器 */
    __compressor_level_deflate_conv(compressor, level);
    compressor->deflate = __compressor_deflate_creat(compressor->level);
    if (!compressor->deflate)
        return COMP_FAIL;

    return COMP_OK;
}

static size_t comp_data_deflate(struct comp_compressor *compressor,
                                const char *in,
                                uint32_t src_len,
                                char *out,
                                uint32_t dst_len)
{
    if (unlikely(!compressor))
        return 0;
    return libdeflate_deflate_compress(compressor->deflate, in, src_len,
            (void*)out, dst_len);
}

static size_t comp_data_isal(struct comp_compressor *compressor,
                             const char *in,
                             uint32_t src_len,
                             char *out,
                             uint32_t dst_len)
{
    if (unlikely(!compressor))
        return 0;

    struct i_compressor *isal = compressor->isal;
    isal_deflate_reset(isal);
    isal->end_of_stream = 1;

    isal->avail_in = src_len;
    isal->next_in = (uint8_t*)in;

    isal->avail_out = dst_len;
    isal->next_out = (uint8_t*)out;
    isal_deflate(isal);

    return (dst_len - isal->avail_out);
}

/* 添加压缩输入文件核心节点 */
int comp_input_centre_add(struct comp_input_centre_hdr *icc_hdr,
                          struct comp_input_centre *icc)
{
    if (unlikely(!icc_hdr || !icc))
        return COMP_FAIL;

    /* 头插 */
    icc->next = icc_hdr->icc_list_hdr;
    icc_hdr->icc_list_hdr = icc;
    return COMP_OK;
}

/* 依据类型,解析输入文件全路径信息 */
int comp_input_path_parse(struct comp_in_path_info *ipi,
                          char *path,
                          uint32_t dir_base_len,
                          enum compress_input type)
{
    size_t len = strlen(path);
    if (unlikely(len >= sizeof(ipi->path))) {
        return COMP_PATH_OVER;
    }

    char *f;
    if (type == COMPRESS_INPUT_DIR) {
        if (path[len - 1] == '/')
            len--;
        memcpy(ipi->path, path, len);
        f = strrchr(ipi->path, '/') + 1;
        ipi->path_len = f - ipi->path - dir_base_len;
        /* 目录类型最后必须为'/' */
        ipi->path[len++] = '/';
        ipi->path[len] = 0;
        ipi->dir_len = strlen(f) + dir_base_len;
        ipi->suffix_len = ipi->file_len = 0;
    } else {
        /* file/list */
        char *suf;
        memcpy(ipi->path, path, len);
        ipi->path[len] = 0;
        f = strrchr(ipi->path, '/') + 1;   //文件名不含 '/'
        if (unlikely(!f || 0 == (*f))) {
            //printf("path(%s) has no / \n", path);
            return COMP_FAIL;
        }
        suf = strrchr(f, '.');            //后缀名含 '.'，可考虑扩展 '_'等后缀
        if (!suf) {
            ipi->suffix_len = 0;
        } else {
            ipi->suffix_len = strlen(suf);
        }

        ipi->file_len = strlen(f) - ipi->suffix_len + dir_base_len;
        ipi->path_len = f - ipi->path - dir_base_len;
        ipi->dir_len = 0;
    }

    return COMP_OK;
}

void *compressor_creat(enum compressor_type type, enum compress_level level)
{
    /* 压缩器不可多线程共享 */
    struct comp_compressor *compressor = hmem_malloc(sizeof(*compressor));
    if (!compressor) {
        return NULL;
    }
    memset(compressor, 0, sizeof(*compressor));
    compressor->type = type;

    /* 压缩器接口 */
    if (type == COMPRESSOR_TYPE_ISAL) {
        compressor->init = compressor_isal_init;
        compressor->comp_data = comp_data_isal;
        compressor->comp_file[COMPRESS_MODE_ZIP] = compress_file_zip_isal;
        compressor->comp_file[COMPRESS_MODE_GZIP] = compress_file_gz_isal;
        compressor->comp_file[COMPRESS_MODE_TGZ] = compress_file_tgz_isal;
    } else {
        compressor->init = compressor_deflate_init;
        compressor->comp_data = comp_data_deflate;
        compressor->comp_file[COMPRESS_MODE_ZIP] = compress_file_zip_deflate;
        compressor->comp_file[COMPRESS_MODE_GZIP] = compress_file_gz_deflate;
        compressor->comp_file[COMPRESS_MODE_TGZ] = compress_file_tgz_deflate;
    }

    /* 核心压缩器初始化 */
    if (COMP_OK != compressor->init(compressor, level)) {
        hmem_free(compressor);
        return NULL;
    }

    g_comp_pgsz = g_comp_pgsz ? g_comp_pgsz : getpagesize();

    return compressor;
}

int compress_file_stream_file_prepare(struct comp_in_path_info *ipi,
                                      struct comp_output_centre *occ,
                                      FILE **out_fp,
                                      uint8_t **in_buf,
                                      uint8_t **out_buf)
{
    char comp_file_name[COMP_MAX_LEN_256];

    /* 获取压缩文件名 */
    if (unlikely(COMP_OK != __comp_out_file_name_get(ipi, occ, comp_file_name,
                    sizeof(comp_file_name)))) {
        return COMP_FAIL;
    }

    *out_fp = fopen(comp_file_name, "wb");
    if (unlikely(!(*out_fp))) {
        //printf("comp_file_name %s fopen err\n", comp_file_name);
        return COMP_FAIL;
    }

    *in_buf = hmem_malloc(COMP_STREAM_BUF_SZ);
    *out_buf = hmem_malloc(COMP_STREAM_BUF_SZ);
    if (unlikely(!(*in_buf) || !(*out_buf))) {
        //printf("info (%s..) compress malloc err: in_buf or out_buf is NULL\n",
        //        ipi->path);
        return COMP_ERR_ALLOC;
    }
    fflush(0);

    return COMP_OK;
}

void compress_icc_hdr_init(struct comp_input_centre_hdr *icc_hdr, uint8_t input_type)
{
    icc_hdr->type = input_type;
    icc_hdr->icc_list_hdr = NULL;
}

void compress_icc_init(struct comp_input_centre *icc)
{
    icc->inter_name = NULL;
    icc->buf = NULL;
}

void compress_occ_pswd_init(struct comp_output_centre *occ,
                            char *comp_pswd)
{
    occ->comp_pswd = comp_pswd ? strdup(comp_pswd) : NULL;
}

void compress_occ_path_init(struct comp_output_centre *occ,
                            char *comp_path)
{
    occ->comp_path = comp_path ? strdup(comp_path) : NULL;
}

void compress_occ_init(struct comp_output_centre *occ,
                       char *comp_path,
                       char *comp_pswd,
                       uint8_t mode)
{
    occ->mode = mode;
    occ->buf_sz = 0;
    occ->buf = NULL;
    occ->comp_name = NULL;
    compress_occ_path_init(occ, comp_path);
    compress_occ_pswd_init(occ, comp_pswd);
}

void compress_icc_deinit(struct comp_input_centre *icc)
{
    if (icc->inter_name)
        hmem_free(icc->inter_name);
}

void compress_occ_deinit(struct comp_output_centre *occ)
{
    if (occ->buf)
        munmap(occ->buf, COMP_PGUP(occ->map_sz, g_comp_pgsz));

    if (occ->comp_name)
        hmem_free(occ->comp_name);

    if (occ->comp_path)
        hmem_free(occ->comp_path);

    if (occ->comp_pswd)
        hmem_free(occ->comp_pswd);
}

size_t compress_dispose_data(void *compressor,
                             const char *in,
                             uint32_t src_len,
                             char *out,
                             uint32_t dst_len)
{
    struct comp_compressor *comp = compressor;
    return comp->comp_data(comp, in, src_len, out, dst_len);
}

int compress_dispose_file(void *compressor,
                          struct comp_input_centre_hdr *icc_hdr,
                          struct comp_output_centre *occ)
{
    /* 目前仅zip支持压缩包加密 */
    if (occ->comp_pswd && occ->mode != COMPRESS_MODE_ZIP)
        return COMP_NO_SUPPORT_PSWD;

    struct comp_compressor *comp = compressor;
    return comp->comp_file[occ->mode](comp, icc_hdr, occ);
}

/**
 * 创建自定义压缩文件名内存
 *
 * @param occ
 *  压缩输出文件核心信息
 * @param len
 *  自定义压缩文件名预期长度
 * @return
 *  NULL: 创建失败
 *  非NULL: 创建成功返回实际内存.
 */
void *compress_outfile_name_creat(struct comp_output_centre *occ, uint32_t len)
{
    if (!occ->comp_name)
        occ->comp_name = hmem_malloc(len + 1);
    return occ->comp_name;
}

/**
 * 创建自定义解压内文件名内存
 *
 * @param icc
 *  压缩输入文件核心信息
 * @param len
 *  自定义解压内文件名预期长度
 * @return
 *  NULL: 创建失败
 *  非NULL: 创建成功返回实际内存.
 */
void *compress_infile_name_creat(struct comp_input_centre *icc, uint32_t len)
{
    if (!icc->inter_name)
        icc->inter_name = hmem_malloc(len + 1);
    return icc->inter_name;
}

