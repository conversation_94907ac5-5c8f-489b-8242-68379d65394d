#ifndef __COMP_ZIP_H__
#define __COMP_ZIP_H__

/* *
 *
 * zip格式规范参照:
 * https://pkwaredownloads.blob.core.windows.net/pem/APPNOTE.txt
 * https://pkware.cachefly.net/webdocs/APPNOTE/APPNOTE-6.3.3.TXT
 *
 * */

#include <sys/queue.h>
#include "comp.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* pkware加密头部长度 */
#define COMP_ZIP_PKWARE_ENCRYP_HDR_LEN          12
#define COMP_ZIP_PKWARE_KEY0                    (0x12345678)
#define COMP_ZIP_PKWARE_KEY1                    (0x23456789)
#define COMP_ZIP_PKWARE_KEY2                    (0x34567890)

#define COMP_ZIP_LOCAL_HEADER_MAGIC             (0x04034b50)
#define COMP_ZIP_LOCAL_HEADER_EXTRACT_VERSION   (0x0014)
#define COMP_ZIP_LOCAL_HEADER_METHOD            (0x0008)
#define COMP_ZIP_CENTRAL_HEADER_MAGIC           (0x02014b50)
#define COMP_ZIP_CENTRAL_HEADER_EXTRACT_VERSION (0x0014)
#define COMP_ZIP_CENTRAL_HEADER_METHOD          (0x0008)
#define COMP_ZIP_CENTRAL_HEADER_INTERNALATTR    (0x0001)
#define COMP_ZIP_END_HEADER_MAGIC               (0x06054b50)
#define COMP_ZIP_END_HEADER_DISK_TOTAL          (0x0001)
#define COMP_ZIP_END_HEADER_DIR_TOTAL           (0x0001)

struct comp_ziplocal_header {
    uint32_t local_header_magic;
    uint16_t extract_version;
    uint16_t flag;
    uint16_t method;
    uint32_t modetime;
    uint32_t crc32;
    uint32_t compr_size;
    uint32_t uncompr_size;
    uint16_t filename_len;
    uint16_t extra_len;
} __comp_packed;

struct comp_zipcenter_header {
    uint32_t center_header_magic;
    uint16_t made_version;
    uint16_t extract_version;
    uint16_t flag;
    uint16_t method;
    uint32_t modetime;
    uint32_t crc32;
    uint32_t compr_size;
    uint32_t uncompr_size;
    uint16_t filename_len;
    uint16_t extra_len;
    uint16_t file_comment_len;
    uint16_t disknum;
    uint16_t internalattr;
    uint32_t externalattr;
    uint32_t local_header_offset;
} __comp_packed;

struct comp_zipend_header {
    uint32_t end_header_magic;
    uint16_t disknum;
    uint16_t center_disk_num;
    uint16_t center_disk_total;
    uint16_t center_dir_total;
    uint32_t center_dir_size;
    uint32_t center_dir_offset;
    uint16_t commentlen;
} __comp_packed;

#define COMP_ZIP_COMPRESSED_LEN_MAX(compress_len, file_name_len)  \
    (compress_len + sizeof(struct comp_ziplocal_header) +         \
     sizeof(struct comp_zipcenter_header) +                       \
     sizeof(struct comp_zipend_header) + (file_name_len * 2))

/* zip临时数据存储结构,用于多文件压缩场景 */
struct comp_zip_note {
    struct comp_input_centre new_icc;           /**< 针对dir类型的new_fc */
    void *icc;                                  /**< 文件列表单项内容 */
    struct comp_zipcenter_header center_hdr;
    TAILQ_ENTRY(comp_zip_note) next;
};
TAILQ_HEAD(comp_zip_note_list, comp_zip_note);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __COMP_ZIP_H__ */
