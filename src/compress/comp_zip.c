#include <dirent.h>
#include "comp_zip.h"
#include "hmemory.h"

static const uint32_t crc32_tab[256] = {
        0x00000000, 0x77073096, 0xee0e612c, 0x990951ba, 0x076dc419, 0x706af48f, 0xe963a535,
        0x9e6495a3, 0x0edb8832, 0x79dcb8a4, 0xe0d5e91e, 0x97d2d988, 0x09b64c2b, 0x7eb17cbd,
        0xe7b82d07, 0x90bf1d91, 0x1db71064, 0x6ab020f2, 0xf3b97148, 0x84be41de, 0x1adad47d,
        0x6ddde4eb, 0xf4d4b551, 0x83d385c7, 0x136c9856, 0x646ba8c0, 0xfd62f97a, 0x8a65c9ec,
        0x14015c4f, 0x63066cd9, 0xfa0f3d63, 0x8d080df5, 0x3b6e20c8, 0x4c69105e, 0xd56041e4,
        0xa2677172, 0x3c03e4d1, 0x4b04d447, 0xd20d85fd, 0xa50ab56b, 0x35b5a8fa, 0x42b2986c,
        0xdbbbc9d6, 0xacbcf940, 0x32d86ce3, 0x45df5c75, 0xdcd60dcf, 0xabd13d59, 0x26d930ac,
        0x51de003a, 0xc8d75180, 0xbfd06116, 0x21b4f4b5, 0x56b3c423, 0xcfba9599, 0xb8bda50f,
        0x2802b89e, 0x5f058808, 0xc60cd9b2, 0xb10be924, 0x2f6f7c87, 0x58684c11, 0xc1611dab,
        0xb6662d3d, 0x76dc4190, 0x01db7106, 0x98d220bc, 0xefd5102a, 0x71b18589, 0x06b6b51f,
        0x9fbfe4a5, 0xe8b8d433, 0x7807c9a2, 0x0f00f934, 0x9609a88e, 0xe10e9818, 0x7f6a0dbb,
        0x086d3d2d, 0x91646c97, 0xe6635c01, 0x6b6b51f4, 0x1c6c6162, 0x856530d8, 0xf262004e,
        0x6c0695ed, 0x1b01a57b, 0x8208f4c1, 0xf50fc457, 0x65b0d9c6, 0x12b7e950, 0x8bbeb8ea,
        0xfcb9887c, 0x62dd1ddf, 0x15da2d49, 0x8cd37cf3, 0xfbd44c65, 0x4db26158, 0x3ab551ce,
        0xa3bc0074, 0xd4bb30e2, 0x4adfa541, 0x3dd895d7, 0xa4d1c46d, 0xd3d6f4fb, 0x4369e96a,
        0x346ed9fc, 0xad678846, 0xda60b8d0, 0x44042d73, 0x33031de5, 0xaa0a4c5f, 0xdd0d7cc9,
        0x5005713c, 0x270241aa, 0xbe0b1010, 0xc90c2086, 0x5768b525, 0x206f85b3, 0xb966d409,
        0xce61e49f, 0x5edef90e, 0x29d9c998, 0xb0d09822, 0xc7d7a8b4, 0x59b33d17, 0x2eb40d81,
        0xb7bd5c3b, 0xc0ba6cad, 0xedb88320, 0x9abfb3b6, 0x03b6e20c, 0x74b1d29a, 0xead54739,
        0x9dd277af, 0x04db2615, 0x73dc1683, 0xe3630b12, 0x94643b84, 0x0d6d6a3e, 0x7a6a5aa8,
        0xe40ecf0b, 0x9309ff9d, 0x0a00ae27, 0x7d079eb1, 0xf00f9344, 0x8708a3d2, 0x1e01f268,
        0x6906c2fe, 0xf762575d, 0x806567cb, 0x196c3671, 0x6e6b06e7, 0xfed41b76, 0x89d32be0,
        0x10da7a5a, 0x67dd4acc, 0xf9b9df6f, 0x8ebeeff9, 0x17b7be43, 0x60b08ed5, 0xd6d6a3e8,
        0xa1d1937e, 0x38d8c2c4, 0x4fdff252, 0xd1bb67f1, 0xa6bc5767, 0x3fb506dd, 0x48b2364b,
        0xd80d2bda, 0xaf0a1b4c, 0x36034af6, 0x41047a60, 0xdf60efc3, 0xa867df55, 0x316e8eef,
        0x4669be79, 0xcb61b38c, 0xbc66831a, 0x256fd2a0, 0x5268e236, 0xcc0c7795, 0xbb0b4703,
        0x220216b9, 0x5505262f, 0xc5ba3bbe, 0xb2bd0b28, 0x2bb45a92, 0x5cb36a04, 0xc2d7ffa7,
        0xb5d0cf31, 0x2cd99e8b, 0x5bdeae1d, 0x9b64c2b0, 0xec63f226, 0x756aa39c, 0x026d930a,
        0x9c0906a9, 0xeb0e363f, 0x72076785, 0x05005713, 0x95bf4a82, 0xe2b87a14, 0x7bb12bae,
        0x0cb61b38, 0x92d28e9b, 0xe5d5be0d, 0x7cdcefb7, 0x0bdbdf21, 0x86d3d2d4, 0xf1d4e242,
        0x68ddb3f8, 0x1fda836e, 0x81be16cd, 0xf6b9265b, 0x6fb077e1, 0x18b74777, 0x88085ae6,
        0xff0f6a70, 0x66063bca, 0x11010b5c, 0x8f659eff, 0xf862ae69, 0x616bffd3, 0x166ccf45,
        0xa00ae278, 0xd70dd2ee, 0x4e048354, 0x3903b3c2, 0xa7672661, 0xd06016f7, 0x4969474d,
        0x3e6e77db, 0xaed16a4a, 0xd9d65adc, 0x40df0b66, 0x37d83bf0, 0xa9bcae53, 0xdebb9ec5,
        0x47b2cf7f, 0x30b5ffe9, 0xbdbdf21c, 0xcabac28a, 0x53b39330, 0x24b4a3a6, 0xbad03605,
        0xcdd70693, 0x54de5729, 0x23d967bf, 0xb3667a2e, 0xc4614ab8, 0x5d681b02, 0x2a6f2b94,
        0xb40bbe37, 0xc30c8ea1, 0x5a05df1b, 0x2d02ef8d
};

static inline uint32_t crc32_one_byte(uint32_t old_crc, char c)
{
    //return ~crc32_gzip_refl(~old_crc, (uint8_t*)&c, 1);
    return (old_crc >> 8) ^ crc32_tab[(old_crc ^ c) & 0xFF];    //更快
}

static inline size_t comp_zip_deflate_bound(struct d_compressor *deflate,
                                            struct comp_input_centre *icc)
{
    size_t compress_len = libdeflate_deflate_compress_bound(deflate,
            icc->filestat.st_size);

    /* 获取最坏情况的上限压缩字节数 */
    return COMP_ZIP_PKWARE_ENCRYP_HDR_LEN + COMP_ZIP_COMPRESSED_LEN_MAX(compress_len,
            (icc->inter_name ? strlen(icc->inter_name) :
             COMP_IN_FILE_NAME_LEN(&icc->ipi)));
}

static inline size_t comp_zip_deflate_data(struct d_compressor *deflate,
                                           const char *in,
                                           uint32_t src_len,
                                           void *out,
                                           uint32_t dst_len)
{
    return libdeflate_deflate_compress(deflate, in, src_len, out, dst_len);
}

static inline size_t comp_zip_isal_data(struct i_compressor *isal,
                                        const char *in,
                                        uint32_t src_len,
                                        void *out,
                                        uint32_t dst_len)
{
    isal_deflate_reset(isal);
    isal->end_of_stream = 1;

    isal->avail_in = src_len;
    isal->next_in = (uint8_t*)in;

    isal->avail_out = dst_len;
    isal->next_out = out;
    isal_deflate(isal);

    return (dst_len - isal->avail_out);
}

static inline void comp_zip_pkware_update_keys(uint32_t *keys, unsigned char c)
{
    keys[0] = crc32_one_byte(keys[0], c);
    keys[1] = keys[1] + (keys[0] & 0xFF);
    keys[1] = keys[1] * 134775813 + 1;
    keys[2] = crc32_one_byte(keys[2], keys[1] >> 24);
}

static void comp_zip_pkware_init_keys(const char *password, uint32_t *keys)
{
    while (*password) {
        comp_zip_pkware_update_keys(keys, *password++);
    }
}

static inline unsigned char __decrypt_byte(uint32_t *keys)
{
    unsigned temp = keys[2] | 2;
    return (unsigned char)((temp * (temp ^ 1)) >> 8) & 0xFF;
}

static inline void comp_zip_pkware_encryp_hdr_creat(unsigned char *header,
                                                    uint32_t *keys,
                                                    uint32_t crc)
{
    /* 任意值即可 */
    int i;
    for (i = 0; i < COMP_ZIP_PKWARE_ENCRYP_HDR_LEN - 1; i++) {
        //header[i] = rand() % 256; // 随机数据
        header[i] = 0;
    }
    /* crc最低字节 */
    header[COMP_ZIP_PKWARE_ENCRYP_HDR_LEN - 1] = (unsigned char)((crc >> 24) & 0xFF);
}

static void comp_zip_pkware_encrypt_data(unsigned char *data,
                                         size_t length,
                                         uint32_t *keys)
{
    size_t i;
    unsigned char tmp;
    for (i = 0; i < length; i++) {
        tmp = data[i];
        data[i] ^= __decrypt_byte(keys);
        comp_zip_pkware_update_keys(keys, tmp);
    }
}

static inline void comp_zip_pkware_encryp_hdr_init(unsigned char *encryp_hdr,
                                                   uint32_t *keys,
                                                   char *pswd,
                                                   uint32_t crc)
{
    /* 初始化pkware keys */
    comp_zip_pkware_init_keys(pswd, keys);
    /* 加密头部 */
    comp_zip_pkware_encryp_hdr_creat(encryp_hdr, keys, crc);
    comp_zip_pkware_encrypt_data(encryp_hdr, COMP_ZIP_PKWARE_ENCRYP_HDR_LEN, keys);
}

static size_t comp_zip_data_compress(struct comp_compressor *comp,
                                     struct comp_output_centre *occ,
                                     const char *in,
                                     uint32_t src_len,
                                     char *out,
                                     uint32_t dst_len,
                                     uint32_t crc)
{
    uint32_t keys[3] =
        {COMP_ZIP_PKWARE_KEY0, COMP_ZIP_PKWARE_KEY1, COMP_ZIP_PKWARE_KEY2};
    uint32_t len = dst_len;
    char *p = out;

    if (occ->comp_pswd) {
        unsigned char encryp_hdr[COMP_ZIP_PKWARE_ENCRYP_HDR_LEN];
        comp_zip_pkware_encryp_hdr_init(encryp_hdr, keys, occ->comp_pswd, crc);
        memcpy(p, encryp_hdr, sizeof(encryp_hdr));
        p += sizeof(encryp_hdr);
        len -= sizeof(encryp_hdr);
    }

    size_t comp_len;
    if (COMPRESSOR_TYPE_ISAL == comp->type) {
        comp_len = comp_zip_isal_data(comp->isal, in, src_len, p, len);
    } else {
        comp_len = comp_zip_deflate_data(comp->deflate, in, src_len, p, len);
    }
    p += comp_len;

    /* 先压缩再加密 */
    if (occ->comp_pswd) {
        comp_zip_pkware_encrypt_data((unsigned char*)p - comp_len, comp_len, keys);
    }

    return (p - out);
}


/* *
 * @brief 时间转换函数
 * @time_zone：UTC:8, GMT:0
 * */
int ft_second_to_date(const time_t sec, struct tm *tm, int time_zone)
{
    const int k_hours_in_day = 24;
    const int k_minutes_in_hour = 60;
    const int k_days_from_unix_time = 2472632;
    const int k_days_from_year = 153;
    const int k_magic_unkonwn_first = 146097;
    const int k_magic_unkonwn_sec = 1461;
    int a, b, c, d, e, m, i;

    i = (sec / k_minutes_in_hour);
    tm->tm_sec = sec % k_minutes_in_hour;
    tm->tm_min = i % k_minutes_in_hour;
    i /= k_minutes_in_hour;
    tm->tm_hour = (i + time_zone) % k_hours_in_day;
    tm->tm_mday = (i + time_zone) / k_hours_in_day;

    a = tm->tm_mday + k_days_from_unix_time;
    b = (a * 4  + 3) / k_magic_unkonwn_first;
    c = (-b * k_magic_unkonwn_first) / 4 + a;
    d = (c * 4 + 3) / k_magic_unkonwn_sec;
    e = -d * k_magic_unkonwn_sec;
    e = e / 4 + c;
    m = (5 * e + 2) / k_days_from_year;

    tm->tm_mday = -(k_days_from_year * m + 2) / 5 + e + 1;
    tm->tm_mon = (-m / 10) * 12 + m + 2;
    tm->tm_year = b * 100 + d - 6700 + (m / 10);

    return 0;
}

/* 文件时间转换为modetime */
static uint32_t __file_time_to_modetime(time_t file_time)
{
    struct tm time_info;

    ft_second_to_date(file_time, &time_info, 8);
    int year = 1900 + time_info.tm_year;
    int month = 1 + time_info.tm_mon;
    int day = time_info.tm_mday;
    int hour = time_info.tm_hour;
    int minute = time_info.tm_min;
    int sec = time_info.tm_sec;

    if (year >= 1980) {
        year -= 1980;
    } else if (year >= 80) {
        year -= 80;
    }

    return ((day + (32 * month) + (512 * year)) << 16) |
        ((sec / 2) + (32 * minute) + (2048 * hour));
}


static inline void comp_zip_local_head_init(struct comp_input_centre *icc,
                                            struct comp_ziplocal_header *hdr,
                                            uint32_t crc32_v,
                                            uint32_t compr_size,
                                            uint32_t filename_len,
                                            uint8_t is_encrypt)
{
    hdr->local_header_magic = COMP_ZIP_LOCAL_HEADER_MAGIC;
    hdr->extract_version = COMP_ZIP_LOCAL_HEADER_EXTRACT_VERSION;
    hdr->flag = (is_encrypt ? 0x1 : 0);
    hdr->method = COMP_ZIP_LOCAL_HEADER_METHOD;
    hdr->modetime = __file_time_to_modetime(icc->filestat.st_mtime);
    hdr->crc32 = crc32_v;
    hdr->compr_size = compr_size;
    if (crc32_v) {
        hdr->uncompr_size = icc->filestat.st_size;
    } else {
        hdr->uncompr_size = 0;
    }
    hdr->filename_len = filename_len;
    hdr->extra_len = 0;
}

static inline void comp_zip_center_head_init(struct comp_ziplocal_header *local_hdr,
                                             struct comp_zipcenter_header *center_hdr,
                                             uint32_t local_hdr_off)
{
    center_hdr->center_header_magic = COMP_ZIP_CENTRAL_HEADER_MAGIC;
    center_hdr->made_version = 0;
    center_hdr->extract_version = COMP_ZIP_CENTRAL_HEADER_EXTRACT_VERSION;
    center_hdr->flag = local_hdr->flag;
    center_hdr->method = COMP_ZIP_CENTRAL_HEADER_METHOD;
    center_hdr->modetime = local_hdr->modetime;
    center_hdr->crc32 = local_hdr->crc32;
    center_hdr->compr_size = local_hdr->compr_size;
    center_hdr->uncompr_size = local_hdr->uncompr_size;
    center_hdr->filename_len = local_hdr->filename_len;
    center_hdr->extra_len = 0;
    center_hdr->file_comment_len = 0;
    center_hdr->disknum = 0;
    center_hdr->internalattr = COMP_ZIP_CENTRAL_HEADER_INTERNALATTR;
    center_hdr->externalattr = 0;
    center_hdr->local_header_offset = local_hdr_off;
}

static inline void comp_zip_end_head_init(struct comp_zipend_header *end_hdr,
                                          uint16_t disk_total,
                                          uint16_t dir_total,
                                          uint32_t dir_size,
                                          uint32_t dir_off)
{
    end_hdr->end_header_magic = COMP_ZIP_END_HEADER_MAGIC;
    end_hdr->disknum = 0;
    end_hdr->center_disk_num = 0;
    end_hdr->center_disk_total = disk_total;
    end_hdr->center_dir_total = dir_total;
    end_hdr->center_dir_size = dir_size;
    end_hdr->center_dir_offset = dir_off;
    end_hdr->commentlen = 0;
}

static int comp_dispose_zip(struct comp_compressor *comp,
                            struct comp_input_centre *icc,
                            struct comp_output_centre *occ)
{
    int out_nbytes_avail = occ->map_sz;
    size_t max_compressed_len = occ->map_sz;
    char *out = occ->buf;
    /* 获取压缩文件解压后的文件名/文件名长度 */
    char *inter_filename;
    uint32_t inter_filename_len = comp_inter_filename_get(icc, &inter_filename);
    uint32_t crc32_v = crc32_gzip_refl(0, (void*)icc->buf, icc->filestat.st_size);

    /* zip header 先偏移 */
    struct comp_ziplocal_header *local_hdr = (void *)out;

    out += sizeof(*local_hdr);
    out_nbytes_avail -= sizeof(*local_hdr);
    if (out_nbytes_avail <= inter_filename_len)
        return COMP_FAIL;

    memcpy(out, inter_filename, inter_filename_len);
    out += inter_filename_len;
    out_nbytes_avail -= inter_filename_len;

    /* zip data */
    uint32_t complen = comp_zip_data_compress(comp, occ, icc->buf, icc->filestat.st_size,
            out, out_nbytes_avail, crc32_v);
    if (0 == complen) {
        return COMP_FAIL;
    }
    out += complen;
    out_nbytes_avail -= complen;
    if (out_nbytes_avail <= 0) {
        return COMP_FAIL;
    }

    /* zip header 初始化 */
    comp_zip_local_head_init(icc, local_hdr, crc32_v, complen,
            inter_filename_len, occ->comp_pswd ? 1 : 0);

    /* zip center header */
    struct comp_zipcenter_header *center_hdr = (void *)out;
    comp_zip_center_head_init(local_hdr, center_hdr, 0);
    out += sizeof(*center_hdr);
    out_nbytes_avail -= sizeof(*center_hdr);
    if (out_nbytes_avail <= inter_filename_len)
        return COMP_FAIL;

    memcpy(out, inter_filename, inter_filename_len);
    out += inter_filename_len;
    out_nbytes_avail -= inter_filename_len;

    /* zip end header */
    int off = sizeof(*local_hdr) + complen + inter_filename_len;
    struct comp_zipend_header *end_hdr = (void *)out;
    comp_zip_end_head_init(end_hdr, COMP_ZIP_END_HEADER_DISK_TOTAL,
            COMP_ZIP_END_HEADER_DIR_TOTAL, sizeof(*center_hdr) + inter_filename_len,
            off);
    out += sizeof(*end_hdr);
    out_nbytes_avail -= sizeof(*end_hdr);
    if (out_nbytes_avail < 0)
        return COMP_FAIL;

    occ->buf_sz = (max_compressed_len - out_nbytes_avail);
    return COMP_OK;
}

int compress_file_zip_deflate(struct comp_compressor *compressor,
                              struct comp_input_centre_hdr *icc_hdr,
                              struct comp_output_centre *occ)
{
    /* deflate方式目前不支持list/dir */
    if (unlikely(icc_hdr->type != COMPRESS_INPUT_FILE || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* deflate方式，输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_zip_deflate_bound(compressor->deflate, icc);

    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_dispose_zip(compressor, icc, occ)) {
            //printf("%s deflate zip err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_dispose_zip(compressor, icc, occ)) {
            //printf("%s deflate zip err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

size_t comp_zip_isal_bound(struct comp_input_centre *icc)
{
    size_t in_nbytes = icc->filestat.st_size;
    size_t max_num_blocks =
        COMP_MAX(COMP_DIV_ROUND_UP(in_nbytes, COMP_MIN_BLOCK_LENGTH), 1);
    size_t compress_len = (5 * max_num_blocks) + in_nbytes + 1 + COMP_OUTPUT_END_PADDING;

    /* 获取最坏情况的上限压缩字节数 */
    return COMP_ZIP_PKWARE_ENCRYP_HDR_LEN + 3 * COMP_ZIP_COMPRESSED_LEN_MAX(compress_len,
            (icc->inter_name ? strlen(icc->inter_name) :
             COMP_IN_FILE_NAME_LEN(&icc->ipi)));
}

static int compress_file_zip_isal_mem(struct comp_compressor *compressor,
                                      struct comp_input_centre_hdr *icc_hdr,
                                      struct comp_output_centre *occ)
{
    /* isal目前只有file类型基于内存压缩 */
    if (unlikely(icc_hdr->type != COMPRESS_INPUT_FILE || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* 输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_zip_isal_bound(icc);

    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_dispose_zip(compressor, icc, occ)) {
            //printf("%s isal zip err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_dispose_zip(compressor, icc, occ)) {
            //printf("%s isal zip err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

static void __comp_zip_note_init(struct comp_zip_note *note,
                                 struct comp_ziplocal_header *local_hdr,
                                 uint32_t local_hdr_off)
{
    comp_zip_center_head_init(local_hdr, &note->center_hdr, local_hdr_off);
}

static int __comp_file_zip_isal_stream_dir_fc(struct i_compressor *isal,
                                              struct comp_zip_note *note,
                                              FILE *out_fp,
                                              uint8_t *in_buf,
                                              uint8_t *out_buf,
                                              uint32_t local_hdr_off)
{
    uint32_t file_sz = 0;
    struct comp_input_centre *icc = note->icc;

    /* 获取压缩文件解压后的文件名/文件名长度 */
    uint32_t inter_filename_len = icc->ipi.dir_len;
    char *inter_filename = COMP_IN_FILE_NAME(&icc->ipi);

    /* zip header: 文件夹不需加密头 */
    struct comp_ziplocal_header local_hdr;
    fseek(out_fp, local_hdr_off, SEEK_SET);
    comp_zip_local_head_init(icc, &local_hdr, 0, 0, inter_filename_len, 0);
    fwrite(&local_hdr, sizeof(local_hdr), 1, out_fp);
    fwrite(inter_filename, inter_filename_len, 1, out_fp);
    file_sz += (sizeof(local_hdr) + inter_filename_len);

    /* zip center header creat */
    __comp_zip_note_init(note, &local_hdr, local_hdr_off);

    return file_sz;
}

static int __comp_file_zip_isal_stream_file_fc(struct i_compressor *isal,
                                               struct comp_zip_note *note,
                                               FILE *out_fp,
                                               char *pswd,
                                               uint8_t *in_buf,
                                               uint8_t *out_buf,
                                               uint32_t local_hdr_off)
{
    struct comp_input_centre *icc = note->icc;
    FILE *in_fp = fopen(icc->ipi.path, "rb");
    if (unlikely(!(in_fp))) {
        //printf("info %s fopen err\n", icc->ipi.path);
        return 0;
    }

    uint32_t file_sz = 0;
    uint32_t complen = 0;
    uint32_t crc32_v = 0;
    uint32_t inter_filename_len;
    char *inter_filename;

    /* 获取压缩文件解压后的文件名/文件名长度 */
    inter_filename_len = comp_inter_filename_get(icc, &inter_filename);

    /* 头部预填充 */
    struct comp_ziplocal_header local_hdr;
    fseek(out_fp, sizeof(local_hdr) + local_hdr_off, SEEK_SET);
    fwrite(inter_filename, inter_filename_len, 1, out_fp);
    file_sz += inter_filename_len;

    uint32_t keys[3] =
        {COMP_ZIP_PKWARE_KEY0, COMP_ZIP_PKWARE_KEY1, COMP_ZIP_PKWARE_KEY2};
    if (pswd) {
        /* 压缩包需加密时，提前计算crc，并构造pkware加密头部 */
        size_t read_len;
        unsigned char encryp_hdr[COMP_ZIP_PKWARE_ENCRYP_HDR_LEN];
        while (!feof(in_fp)) {
            read_len = fread(in_buf, 1, COMP_STREAM_BUF_SZ, in_fp);
            crc32_v = crc32_gzip_refl(crc32_v, in_buf, read_len);
        }
        comp_zip_pkware_encryp_hdr_init(encryp_hdr, keys, pswd, crc32_v);
        fwrite(encryp_hdr, 1, sizeof(encryp_hdr), out_fp);
        complen += sizeof(encryp_hdr);
        fseek(in_fp, 0, SEEK_SET);
    }

    isal_deflate_reset(isal);
    /* compress data */
    do {
        isal->avail_in = (uint32_t) fread(in_buf, 1, COMP_STREAM_BUF_SZ, in_fp);
        isal->end_of_stream = feof(in_fp) ? 1 : 0;
        isal->next_in = in_buf;

        if (!pswd) {
            /* 非加密包时候，crc32计算在压缩过程中进行 */
            crc32_v = crc32_gzip_refl(crc32_v, in_buf, isal->avail_in);
        }

        do {
            isal->avail_out = COMP_STREAM_BUF_SZ;
            isal->next_out = out_buf;
            isal_deflate(isal);
            if (pswd) {
                comp_zip_pkware_encrypt_data(out_buf, COMP_STREAM_BUF_SZ -
                        isal->avail_out, keys);
            }
            fwrite(out_buf, 1, COMP_STREAM_BUF_SZ - isal->avail_out, out_fp);
            complen += (COMP_STREAM_BUF_SZ - isal->avail_out);
        } while (isal->avail_out == 0);

        if (unlikely(isal->avail_in != 0)) {
            //printf("info %s isal avail_in not 0 (%u)\n", icc->ipi.path, isal->avail_in);
            fclose(in_fp);
            return 0;
        }
    } while (COMP_ISAL_STREAM_NOT_END(isal));

    /* zip header */
    comp_zip_local_head_init(icc, &local_hdr, crc32_v, complen,
            inter_filename_len, pswd ? 1 : 0);
    fseek(out_fp, local_hdr_off, SEEK_SET);
    fwrite(&local_hdr, sizeof(local_hdr), 1, out_fp);
    file_sz += (complen + sizeof(local_hdr));

    /* zip center header creat */
    __comp_zip_note_init(note, &local_hdr, local_hdr_off);

    fclose(in_fp);

    return file_sz;
}

static int __compress_file_zip_isal_stream_dir(struct i_compressor *isal,
                                               struct comp_zip_note *dir_note,
                                               struct comp_zip_note_list *note_list,
                                               char *pswd,
                                               uint32_t *local_hdr_off,
                                               FILE *out_fp,
                                               uint8_t *in_buf,
                                               uint8_t *out_buf)
{
#define COMP_DIR_ERR_RET(note, dir)     \
    do {                                \
        hmem_free(note);                \
        closedir(dir);                  \
        return COMP_FAIL;               \
    } while (0)

    struct comp_input_centre *icc = dir_note->icc;
    DIR *dir = opendir(icc->ipi.path);
    if (!dir) {
        //printf("opendir %s err\n", icc->ipi.path);
        return COMP_FAIL;
    }

    /* 压缩目录,note加入缓存链 */
    size_t len = __comp_file_zip_isal_stream_dir_fc(isal, dir_note, out_fp,
            in_buf, out_buf, *local_hdr_off);
    (*local_hdr_off) += len;
    TAILQ_INSERT_TAIL(note_list, dir_note, next);

    char fullpath[COMP_MAX_LEN_512];
    struct dirent *entry;
    struct comp_zip_note *note;
    while ((entry = readdir(dir))) {
        /* 过滤 ".", ".." 目录 */
        if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, "..")) {
            continue;
        }

        /* 创建note，获取文件基本信息,初始化new_icc */
        note = hmem_malloc(sizeof(*note));
        if (unlikely(!note)) {
            //printf("zip_note malloc fail\n");
            closedir(dir);
            return COMP_ERR_ALLOC;
        }
        compress_icc_init(&note->new_icc);

        snprintf(fullpath, sizeof(fullpath), "%s%s", icc->ipi.path, entry->d_name);
        if (-1 == stat(fullpath, &note->new_icc.filestat)) {
            //printf("%s get stat err\n", fullpath);
            COMP_DIR_ERR_RET(note, dir);
        }
        note->icc = &note->new_icc;

        if (S_ISREG(note->new_icc.filestat.st_mode)) {
            /* 普通文件 */
            if (COMP_OK != comp_input_path_parse(&note->new_icc.ipi, fullpath,
                        icc->ipi.dir_len, COMPRESS_INPUT_FILE)) {
                COMP_DIR_ERR_RET(note, dir);
            }
            len = __comp_file_zip_isal_stream_file_fc(isal, note, out_fp, pswd, in_buf,
                    out_buf, *local_hdr_off);
            if (unlikely(!len)) {
                //printf("compress data zip isal stream file icc err\n");
                COMP_DIR_ERR_RET(note, dir);
            }
            (*local_hdr_off) += len;
            TAILQ_INSERT_TAIL(note_list, note, next);
        } else if (S_ISDIR(note->new_icc.filestat.st_mode)) {
            /* dir文件 */
            if (COMP_OK != comp_input_path_parse(&note->new_icc.ipi, fullpath,
                        icc->ipi.dir_len, COMPRESS_INPUT_DIR)) {
                COMP_DIR_ERR_RET(note, dir);
            }
            if (COMP_OK != __compress_file_zip_isal_stream_dir(isal, note, note_list,
                        pswd, local_hdr_off, out_fp, in_buf, out_buf)) {
                COMP_DIR_ERR_RET(note, dir);
            }
        } else {
            /* 其它类型不处理 */
            //printf("unsupport file type : %s\n", fullpath);
            hmem_free(note);
        }
    }

    return COMP_OK;
#undef COMP_DIR_ERR_RET
}

static inline uint32_t
__compress_zip_center_end_stream(struct comp_zip_note_list *note_list,
                                 FILE *out_fp,
                                 uint32_t local_hdr_off)
{
    uint32_t inter_filename_len;
    uint32_t center_total = 0;
    size_t center_dir_size = 0;
    char *inter_filename;
    struct comp_input_centre *icc;
    struct comp_zip_note *note;

    /* center hdr */
    TAILQ_FOREACH (note, note_list, next) {
        icc = note->icc;
        /* 获取压缩文件解压后的文件名/文件名长度 */
        if (icc->ipi.file_len) {
            inter_filename_len = comp_inter_filename_get(icc, &inter_filename);
        } else {
            inter_filename_len = icc->ipi.dir_len;
            inter_filename = COMP_IN_FILE_NAME(&icc->ipi);
        }
        fwrite(&note->center_hdr, sizeof(note->center_hdr), 1, out_fp);
        fwrite(inter_filename, inter_filename_len, 1, out_fp);
        center_dir_size += (inter_filename_len + sizeof(note->center_hdr));
        center_total++;
    }

    /* zip end header */
    struct comp_zipend_header end_hdr;
    comp_zip_end_head_init(&end_hdr, center_total, center_total, center_dir_size,
            local_hdr_off);
    fwrite(&end_hdr, sizeof(end_hdr), 1, out_fp);

    return (center_dir_size + sizeof(end_hdr));
}

static int compress_file_zip_isal_stream_dir(struct comp_compressor *comp,
                                             struct comp_input_centre_hdr *icc_hdr,
                                             struct comp_output_centre *occ)
{
    int ret;
    FILE *out_fp = NULL;
    uint8_t *in_buf = NULL, *out_buf = NULL;
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    struct comp_zip_note *note, *tmp;
    struct comp_zip_note_list note_list;

    TAILQ_INIT(&note_list);

    /* 压缩准备 */
    if (COMP_OK != (ret = compress_file_stream_file_prepare(&icc->ipi, occ, &out_fp,
                    &in_buf, &out_buf))) {
        goto done;
    }

    //printf("zip compress dir %s\n", icc->ipi.path);

    uint32_t local_hdr_off = 0;
    struct i_compressor *isal = comp->isal;

    /* 创建起始目录note，开始目录压缩 */
    note = hmem_malloc(sizeof(*note));
    if (unlikely(!note)) {
        ret = COMP_ERR_ALLOC;
        //printf("zip_note malloc fail\n");
        goto done;
    }
    note->icc = icc;
    if (COMP_OK != (ret = __compress_file_zip_isal_stream_dir(isal, note, &note_list,
                occ->comp_pswd, &local_hdr_off, out_fp, in_buf, out_buf))) {
        hmem_free(note);
        goto done;
    }

    /* center + end 将由结尾写入 */
    fseek(out_fp, 0, SEEK_END);
    uint32_t center_end_sz = __compress_zip_center_end_stream(&note_list, out_fp,
            local_hdr_off);
    occ->buf_sz = (local_hdr_off + center_end_sz);

    ret = COMP_OK;
done:
    if (out_fp)
        fclose(out_fp);
    if (in_buf)
        hmem_free(in_buf);
    if (out_buf)
        hmem_free(out_buf);

    /* 回收内存 */
    TAILQ_FOREACH_SAFE (note, &note_list, next, tmp) {
        hmem_free(note);
    }

    return ret;
}

static int compress_file_zip_isal_stream_file(struct comp_compressor *comp,
                                              struct comp_input_centre_hdr *icc_hdr,
                                              struct comp_output_centre *occ)
{
    int ret;
    FILE *out_fp = NULL;
    uint8_t *in_buf = NULL, *out_buf = NULL;
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    struct comp_zip_note *note, *tmp;
    struct comp_zip_note_list note_list;

    TAILQ_INIT(&note_list);

    /* 压缩准备 */
    if (COMP_OK != (ret = compress_file_stream_file_prepare(&icc->ipi, occ, &out_fp,
                    &in_buf, &out_buf))) {
        goto done;
    }

    size_t len;
    size_t local_hdr_off = 0;
    struct i_compressor *isal = comp->isal;

    while (icc) {
        //printf("zip compress file %s\n", icc->ipi.path);
        note = hmem_malloc(sizeof(*note));
        if (unlikely(!note)) {
            ret = COMP_ERR_ALLOC;
            //printf("zip_note malloc fail\n");
            goto done;
        }
        /* local_hdr + data + filename + center creat(note) */
        note->icc = icc;
        len = __comp_file_zip_isal_stream_file_fc(isal, note, out_fp, occ->comp_pswd,
                in_buf, out_buf, local_hdr_off);
        if (unlikely(!len)) {
            hmem_free(note);
            //printf("compress data zip isal stream file icc err\n");
            goto done;
        }
        local_hdr_off += len;
        TAILQ_INSERT_TAIL(&note_list, note, next);
        icc = icc->next;
    }

    /* center + end 将由结尾写入 */
    fseek(out_fp, 0, SEEK_END);
    uint32_t center_end_sz = __compress_zip_center_end_stream(&note_list, out_fp,
            local_hdr_off);
    occ->buf_sz = (local_hdr_off + center_end_sz);

    ret = COMP_OK;

done:
    if (out_fp)
        fclose(out_fp);
    if (in_buf)
        hmem_free(in_buf);
    if (out_buf)
        hmem_free(out_buf);

    /* 回收内存 */
    TAILQ_FOREACH_SAFE (note, &note_list, next, tmp) {
        hmem_free(note);
    }

    return ret;
}

int compress_file_zip_isal(struct comp_compressor *compressor,
                           struct comp_input_centre_hdr *icc_hdr,
                           struct comp_output_centre *occ)
{
    if (unlikely(!icc_hdr || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    if (icc_hdr->icc_list_hdr->buf) {
        /* 基于内存压缩 */
        //printf("zip compress file %s, isal by mem\n", icc_hdr->icc_list_hdr->ipi.path);
        return compress_file_zip_isal_mem(compressor, icc_hdr, occ);
    } else {
        /* 基于文件流压缩 */
        if (COMPRESS_INPUT_DIR == icc_hdr->type) {
            //printf("zip compress dir %s, isal by stream\n",
            //icc_hdr->icc_list_hdr->ipi.path);
            return compress_file_zip_isal_stream_dir(compressor, icc_hdr, occ);
        } else {
            //printf("zip compress file (%s..), isal by stream\n",
            //icc_hdr->icc_list_hdr->ipi.path);
            return compress_file_zip_isal_stream_file(compressor, icc_hdr, occ);
        }
    }
}

