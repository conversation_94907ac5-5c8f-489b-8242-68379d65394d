#include <dirent.h>
#include "comp_tgz.h"

char g_gz_end_block[COMP_T_BLOCKSIZE];

extern uint32_t __compress_isal_gz(struct i_compressor *isal,
                                   void *in, size_t in_nbytes,
                                   void *out, size_t out_nbytes_avail);
extern size_t comp_gz_isal_bound(struct comp_input_centre *icc);

static inline size_t comp_tar_isal_bound(struct comp_input_centre *icc)
{
    /* isal */
    size_t in_nbytes = COMP_T_BLOCKSIZE;
    size_t max_num_blocks =
        COMP_MAX(COMP_DIV_ROUND_UP(in_nbytes, COMP_MIN_BLOCK_LENGTH), 1);
    size_t compress_len = (5 * max_num_blocks) + in_nbytes + 1 + COMP_OUTPUT_END_PADDING;
    uint32_t block_size = sizeof(struct isal_gzip_header) + compress_len;
    uint32_t max_size = comp_gz_isal_bound(icc) + block_size * 2;

    return max_size;
}

/* integer to string-octal conversion, no NULL */
static void comp_th_int_to_oct_nonull(int num, char *oct, size_t octlen)
{
    snprintf(oct, octlen, "%*lo", (int)octlen - 1, (unsigned long)num);
    oct[octlen - 1] = ' ';
}

/* calculate header checksum */
int comp_th_crc_calc(struct comp_tar *t)
{
    int i, sum = 0;

    for (i = 0; i < COMP_T_BLOCKSIZE; i++)
        sum += ((unsigned char *)(&(t->th_buf)))[i];
    for (i = 0; i < 8; i++)
        sum += (' ' - (unsigned char)t->th_buf.chksum[i]);

    return sum;
}

/* magic, version, and checksum */
void comp_th_finish(struct comp_tar *t)
{
    memcpy(t->th_buf.version, COMP_T_TVERSION, sizeof(t->th_buf.version));
    memcpy(t->th_buf.magic, COMP_T_TMAGIC, sizeof(t->th_buf.magic));

    comp_th_int_to_oct(comp_th_crc_calc(t), t->th_buf.chksum, 8);
}

/* map a file mode to a typeflag */
static void comp_th_set_type(struct comp_tar *t, mode_t mode)
{
    if (S_ISLNK(mode))
        t->th_buf.typeflag = SYMTYPE;
    if (S_ISREG(mode))
        t->th_buf.typeflag = REGTYPE;
    if (S_ISDIR(mode))
        t->th_buf.typeflag = DIRTYPE;
    if (S_ISCHR(mode))
        t->th_buf.typeflag = CHRTYPE;
    if (S_ISBLK(mode))
        t->th_buf.typeflag = BLKTYPE;
    if (S_ISFIFO(mode) || S_ISSOCK(mode))
        t->th_buf.typeflag = FIFOTYPE;
}

/* encode file path */
void comp_th_set_path(struct comp_tar *t, const char *pathname)
{
    int len = 0;
    char suffix[2] = "";
    char *tmp;

#ifdef DEBUG
    printf("in %s(th, pathname=\"%s\")\n", __func__, pathname);
#endif

    if (strlen(pathname) > COMP_T_NAMELEN) {
        /* POSIX-style prefix field */
        tmp = strchr(&(pathname[strlen(pathname) - COMP_T_NAMELEN - 1]), '/');
        if (tmp == NULL) {
            printf("!!! '/' not found in \"%s\"\n", pathname);
            return;
        }
        snprintf(t->th_buf.name, sizeof(t->th_buf.name), "%s%s", &(tmp[1]), suffix);
        snprintf(t->th_buf.prefix, ((tmp - pathname + 1) < 155 ?
                    (tmp - pathname + 1) : 155), "%s", pathname);
    } else {
        /* classic tar format */
        len = snprintf(t->th_buf.name, sizeof(t->th_buf.name), "%s%s",
                pathname, suffix);
        if(len < 0 || len >= sizeof(t->th_buf.name))
            t->th_buf.name[0] = '\0';
    }

#ifdef DEBUG
    puts("returning from comp_th_set_path...");
#endif
}

/* encode device info */
static void comp_th_set_device(struct comp_tar *t, dev_t device)
{
#ifdef DEBUG
    printf("%s(): major = %d, minor = %d\n", __func__, major(device), minor(device));
#endif
    comp_th_int_to_oct(major(device), t->th_buf.devmajor, 8);
    comp_th_int_to_oct(minor(device), t->th_buf.devminor, 8);
}

/* encode user info */
static void comp_th_set_user(struct comp_tar *t, uid_t uid)
{
    struct passwd *pw;

    pw = getpwuid(uid);
    if (pw != NULL)
        snprintf(t->th_buf.uname, sizeof(t->th_buf.uname), "%s", pw->pw_name);

    comp_th_int_to_oct(uid, t->th_buf.uid, 8);
}

/* encode group info */
static void comp_th_set_group(struct comp_tar *t, gid_t gid)
{
    struct group *gr;

    gr = getgrgid(gid);
    if (gr != NULL)
        snprintf(t->th_buf.gname, sizeof(t->th_buf.gname), "%s", gr->gr_name);

    comp_th_int_to_oct(gid, t->th_buf.gid, 8);
}

/* encode file mode */
static void comp_th_set_mode(struct comp_tar *t, mode_t fmode)
{
    if (S_ISSOCK(fmode)) {
        fmode &= ~S_IFSOCK;
        fmode |= S_IFIFO;
    }
    comp_th_int_to_oct(fmode, (t)->th_buf.mode, 8);
}

void comp_th_set_from_stat(struct comp_tar *t, struct stat *s)
{
    comp_th_set_type(t, s->st_mode);
    if (S_ISCHR(s->st_mode) || S_ISBLK(s->st_mode))
        comp_th_set_device(t, s->st_rdev);
    comp_th_set_user(t, s->st_uid);
    comp_th_set_group(t, s->st_gid);
    comp_th_set_mode(t, s->st_mode);
    comp_th_set_mtime(t, s->st_mtime);
    if (S_ISREG(s->st_mode))
        comp_th_set_size(t, s->st_size);
    else
        comp_th_set_size(t, 0);
}

static inline size_t comp_tar_deflate_bound(struct d_compressor *deflate,
                                            struct comp_input_centre *icc)
{
    uint32_t block_size = libdeflate_gzip_compress_bound(deflate, COMP_T_BLOCKSIZE);
    uint32_t max_size = libdeflate_gzip_compress_bound(deflate,
            icc->filestat.st_size) + block_size * 2;
    return max_size;
}

static int comp_tar_isal_data(struct i_compressor *isal,
                              struct comp_input_centre *icc,
                              struct comp_output_centre *occ,
                              struct comp_tar *t_hand)
{
    int len, off = 0;

    /* th_buf */
    len = __compress_isal_gz(isal, (char *)&t_hand->th_buf,
            COMP_T_BLOCKSIZE, occ->buf + off, occ->map_sz - off);
    if (0 == len)
        return COMP_FAIL;
    off += len;

    /* compress file data */
    len = __compress_isal_gz(isal, icc->buf, icc->filestat.st_size,
            occ->buf + off, occ->map_sz - off);
    if (0 == len)
        return COMP_FAIL;
    off += len;

    /* add eof */
    size_t unalign_sz = icc->filestat.st_size % COMP_T_BLOCKSIZE;
    if (unalign_sz) {
        size_t blk_sz = COMP_T_BLOCKSIZE - unalign_sz;
        len = __compress_isal_gz(isal, g_gz_end_block, blk_sz, occ->buf + off,
                occ->map_sz - off);
        if (0 == len)
            return COMP_FAIL;
        off += len;
    }

    occ->buf_sz = off;
    return COMP_OK;
}

static int comp_tar_deflate_data(struct d_compressor *deflate,
                                 struct comp_input_centre *icc,
                                 struct comp_output_centre *occ,
                                 struct comp_tar *t_hand)
{
    int len, off = 0;

    len = libdeflate_gzip_compress(deflate, (char *)&t_hand->th_buf,
            COMP_T_BLOCKSIZE, occ->buf + off, occ->map_sz - off);
    if (0 == len)
        return COMP_FAIL;
    off += len;

    /* compress file data */
    len = libdeflate_gzip_compress(deflate, icc->buf, icc->filestat.st_size,
            occ->buf + off, occ->map_sz - off);
    if (0 == len)
        return COMP_FAIL;
    off += len;

    /* add eof */
    size_t unalign_sz = icc->filestat.st_size % COMP_T_BLOCKSIZE;
    if (unalign_sz) {
        size_t blk_sz = COMP_T_BLOCKSIZE - unalign_sz;
        len = libdeflate_gzip_compress(deflate, g_gz_end_block, blk_sz, occ->buf + off,
                occ->map_sz - off);
        if (0 == len)
            return COMP_FAIL;
        off += len;
    }

    occ->buf_sz = off;
    return COMP_OK;
}

static inline int comp_dispose_tar(struct comp_compressor *comp,
                                   struct comp_input_centre *icc,
                                   struct comp_output_centre *occ)
{
    char *filename;
    struct comp_tar t_hand;

    /* 获取压缩文件解压后的文件名/文件名长度 */
    comp_inter_filename_get(icc, &filename);

    memset(&t_hand, 0, sizeof(struct comp_tar));
    comp_th_set_from_stat(&t_hand, &icc->filestat);
    comp_th_set_path(&t_hand, filename);
    comp_th_finish(&t_hand);
    if (COMPRESSOR_TYPE_ISAL == comp->type) {
        if (COMP_OK != comp_tar_isal_data(comp->isal, icc, occ, &t_hand))
            return COMP_FAIL;
    } else {
        if (COMP_OK != comp_tar_deflate_data(comp->deflate, icc, occ, &t_hand))
            return COMP_FAIL;
    }

    return COMP_OK;
}

int compress_file_tgz_deflate(struct comp_compressor *comp,
                              struct comp_input_centre_hdr *icc_hdr,
                              struct comp_output_centre *occ)
{
    /* deflate方式目前不支持list/dir */
    if (unlikely(icc_hdr->type != COMPRESS_INPUT_FILE || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* deflate方式，输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_tar_deflate_bound(comp->deflate, icc);
    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_dispose_tar(comp, icc, occ)) {
            //printf("%s deflate tar.gz err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_dispose_tar(comp, icc, occ)) {
            //printf("%s deflate tar.gz err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

static int compress_file_tgz_isal_mem(struct comp_compressor *compressor,
                                      struct comp_input_centre_hdr *icc_hdr,
                                      struct comp_output_centre *occ)
{
    /* isal目前只有file类型基于内存压缩 */
    if (unlikely(icc_hdr->type != COMPRESS_INPUT_FILE || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;
    /* 输入文件必须提前映射内存 */
    if (unlikely(!icc->buf))
        return COMP_FAIL;

    /* 预计算空间大小，并映射 */
    occ->map_sz = comp_tar_isal_bound(icc);
    if (!occ->comp_path) {
        /* 关联内存 */
        occ->buf = __out_mem_mmap(-1, occ->map_sz);
        if (!occ->buf)
            return COMP_FAIL;
        /* 压缩 */
        if (COMP_OK != comp_dispose_tar(compressor, icc, occ)) {
            //printf("%s isal tar.gz err\n", icc->ipi.path);
            return COMP_FAIL;
        }
    } else {
        int fd = comp_write_out_file_mmap(&icc->ipi, occ);
        if (0 > fd)
            return COMP_FAIL;

        /* 压缩 */
        if (COMP_OK != comp_dispose_tar(compressor, icc, occ)) {
            //printf("%s isal tar.gz err\n", icc->ipi.path);
            close(fd);
            return COMP_FAIL;
        }

        /* 重新修改文件大小 */
        if (ftruncate(fd, occ->buf_sz) < 0) {
            //printf("afresh ftruncate err\n");
            close(fd);
            return COMP_FAIL;
        }
        close(fd);

        /* 同步压缩文件到磁盘 */
        compress_outfile_sync_local(occ);
    }

    return COMP_OK;
}

static int __comp_file_tgz_isal_stream_dir_fc(struct i_compressor *isal,
                                              struct comp_input_centre *icc,
                                              FILE *out_fp,
                                              uint8_t *out_buf)
{
    /* 获取压缩文件解压后的文件名 */
    char *filename = COMP_IN_FILE_NAME(&icc->ipi);

    /* t_hand init */
    struct comp_tar t_hand;
    memset(&t_hand, 0, sizeof(struct comp_tar));
    comp_th_set_from_stat(&t_hand, &icc->filestat);
    comp_th_set_path(&t_hand, filename);
    comp_th_finish(&t_hand);

    uint32_t file_sz = 0;
    /* th_buf */
    size_t len = __compress_isal_gz(isal, &t_hand.th_buf, COMP_T_BLOCKSIZE, out_buf,
            COMP_STREAM_BUF_SZ);
    if (0 == len) {
        //printf("compress data tgz isal stream dir fc err\n");
        return 0;
    }
    fwrite(out_buf, 1, len, out_fp);
    file_sz += len;

    return file_sz;
}

static int __comp_file_tgz_isal_stream_file_fc(struct i_compressor *isal,
                                               struct comp_input_centre *icc,
                                               FILE *out_fp,
                                               uint8_t *in_buf,
                                               uint8_t *out_buf)
{
    FILE *in_fp = fopen(icc->ipi.path, "rb");
    if (unlikely(!(in_fp))) {
        //printf("info %s fopen err\n", icc->ipi.path);
        return 0;
    }

    /* 获取压缩文件解压后的文件名 */
    char *filename;
    comp_inter_filename_get(icc, &filename);

    /* t_hand init */
    struct comp_tar t_hand;

    memset(&t_hand, 0, sizeof(struct comp_tar));
    comp_th_set_from_stat(&t_hand, &icc->filestat);
    comp_th_set_path(&t_hand, filename);
    comp_th_finish(&t_hand);

    int len;
    uint32_t file_sz = 0;
    /* th_buf */
    len = __compress_isal_gz(isal, &t_hand.th_buf, COMP_T_BLOCKSIZE, out_buf,
            COMP_STREAM_BUF_SZ);
    if (0 == len) {
        fclose(in_fp);
        return 0;
    }
    fwrite(out_buf, 1, len, out_fp);
    file_sz += len;

    /* compress file data */
    uint32_t crc32_v = 0;
    isal_deflate_reset(isal);
        /* gz_hdr */
    struct isal_gzip_header gz_hdr;
    isal->avail_in = 0;
    isal->next_out = out_buf;
    isal->avail_out = COMP_STREAM_BUF_SZ;
    isal_gzip_header_init(&gz_hdr);
    isal_write_gzip_header(isal, &gz_hdr);
    fwrite(out_buf, 1, isal->total_out, out_fp);
    file_sz += isal->total_out;
        /* data */
    do {
        isal->avail_in = (uint32_t) fread(in_buf, 1, COMP_STREAM_BUF_SZ, in_fp);
        isal->end_of_stream = feof(in_fp) ? 1 : 0;
        isal->next_in = in_buf;
        /* crc32计算 */
        crc32_v = crc32_gzip_refl(crc32_v, in_buf, isal->avail_in);
        do {
            isal->avail_out = COMP_STREAM_BUF_SZ;
            isal->next_out = out_buf;
            isal_deflate(isal);
            fwrite(out_buf, 1, COMP_STREAM_BUF_SZ - isal->avail_out, out_fp);
            file_sz += (COMP_STREAM_BUF_SZ - isal->avail_out);
        } while (isal->avail_out == 0);

        if (unlikely(isal->avail_in != 0)) {
            //printf("info %s isal avail_in not 0 (%u)\n", icc->ipi.path, isal->avail_in);
            fclose(in_fp);
            return 0;
        }
    } while (COMP_ISAL_STREAM_NOT_END(isal));
        /* crc32 + insize*/
    fwrite(&crc32_v, 1, sizeof(uint32_t), out_fp);
    fwrite(&icc->filestat.st_size, 1, sizeof(uint32_t), out_fp);
    file_sz += (sizeof(uint32_t) * 2);

    /* add eof */
    size_t unalign_sz = icc->filestat.st_size % COMP_T_BLOCKSIZE;
    if (unalign_sz) {
        size_t blk_sz = COMP_T_BLOCKSIZE - unalign_sz;
        len = __compress_isal_gz(isal, g_gz_end_block, blk_sz, out_buf,
                COMP_STREAM_BUF_SZ);
        if (0 == len) {
            fclose(in_fp);
            return 0;
        }
        fwrite(out_buf, 1, len, out_fp);
        file_sz += len;
    }

    fclose(in_fp);
    return file_sz;
}

static int __compress_file_tgz_isal_stream_dir(struct i_compressor *isal,
                                               struct comp_output_centre *occ,
                                               struct comp_input_centre *icc,
                                               FILE *out_fp,
                                               uint8_t *in_buf,
                                               uint8_t *out_buf)
{
#define COMP_DIR_ERR_RET(dir)  \
    do {                       \
        closedir(dir);         \
        return COMP_FAIL;        \
    } while (0)

    DIR *dir = opendir(icc->ipi.path);
    if (!dir) {
        //printf("opendir %s err\n", icc->ipi.path);
        return COMP_FAIL;
    }

    /* 压缩目录 */
    size_t len = __comp_file_tgz_isal_stream_dir_fc(isal, icc, out_fp, out_buf);
    if (0 == len) {
        COMP_DIR_ERR_RET(dir);
    }
    occ->buf_sz += len;

    char fullpath[COMP_MAX_LEN_512];
    struct comp_input_centre new_icc;
    struct dirent *entry;

    /* 递归目录 */
    while ((entry = readdir(dir))) {
        /* 过滤 ".", ".." 目录 */
        if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, "..")) {
            continue;
        }
        compress_icc_init(&new_icc);

        /* 获取文件基本信息 */
        snprintf(fullpath, sizeof(fullpath), "%s%s", icc->ipi.path, entry->d_name);
        if (-1 == stat(fullpath, &new_icc.filestat)) {
            //printf("%s get stat err\n", fullpath);
            COMP_DIR_ERR_RET(dir);
        }

        if (S_ISREG(new_icc.filestat.st_mode)) {
            /* 普通文件 */
            if (COMP_OK != comp_input_path_parse(&new_icc.ipi, fullpath, icc->ipi.dir_len,
                        COMPRESS_INPUT_FILE)) {
                COMP_DIR_ERR_RET(dir);
            }
            len = __comp_file_tgz_isal_stream_file_fc(isal, &new_icc, out_fp, in_buf,
                    out_buf);
            if (unlikely(!len)) {
                //printf("compress data tgz isal stream file fc err\n");
                COMP_DIR_ERR_RET(dir);
            }
            occ->buf_sz += len;
        } else if (S_ISDIR(new_icc.filestat.st_mode)) {
            /* dir文件 */
            if (COMP_OK != comp_input_path_parse(&new_icc.ipi, fullpath, icc->ipi.dir_len,
                        COMPRESS_INPUT_DIR)) {
                COMP_DIR_ERR_RET(dir);
            }
            if (COMP_OK != __compress_file_tgz_isal_stream_dir(isal, occ, &new_icc,
                        out_fp, in_buf, out_buf)) {
                COMP_DIR_ERR_RET(dir);
            }
        } else {
            /* 其它类型不处理 */
            //printf("unsupport file type : %s\n", fullpath);
        }
    }

    return COMP_OK;
#undef COMP_DIR_ERR_RET
}

static int compress_file_tgz_isal_stream_dir(struct comp_compressor *comp,
                                             struct comp_input_centre_hdr *icc_hdr,
                                             struct comp_output_centre *occ)
{
    int ret;
    FILE *out_fp = NULL;
    uint8_t *in_buf = NULL, *out_buf = NULL;
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;

    /* 压缩准备 */
    if (COMP_OK != (ret = compress_file_stream_file_prepare(&icc->ipi, occ, &out_fp,
                    &in_buf, &out_buf))) {
        goto done;
    }

    //printf("tgz compress dir %s\n", icc->ipi.path);

    struct i_compressor *isal = comp->isal;
    if (COMP_OK != __compress_file_tgz_isal_stream_dir(isal, occ, icc, out_fp,
                in_buf, out_buf)) {
        goto done;
    }
    ret = COMP_OK;

done:
    if (out_fp)
        fclose(out_fp);
    if (in_buf)
        free(in_buf);
    if (out_buf)
        free(out_buf);

    return ret;
}

static int compress_file_tgz_isal_stream_file(struct comp_compressor *comp,
                                              struct comp_input_centre_hdr *icc_hdr,
                                              struct comp_output_centre *occ)
{
    int ret;
    FILE *out_fp = NULL;
    uint8_t *in_buf = NULL, *out_buf = NULL;
    struct comp_input_centre *icc = icc_hdr->icc_list_hdr;

    /* 压缩准备 */
    if (COMP_OK != (ret = compress_file_stream_file_prepare(&icc->ipi, occ, &out_fp,
                    &in_buf, &out_buf))) {
        goto done;
    }

    size_t len;
    struct i_compressor *isal = comp->isal;

    while (icc) {
        //printf("tgz compress file %s\n", icc->ipi.path);
        len = __comp_file_tgz_isal_stream_file_fc(isal, icc, out_fp, in_buf, out_buf);
        if (unlikely(!len)) {
            //printf("compress data tgz isal stream file fc err\n");
            goto done;
        }
        occ->buf_sz += len;
        icc = icc->next;
    }

    ret = COMP_OK;

done:
    if (out_fp)
        fclose(out_fp);
    if (in_buf)
        free(in_buf);
    if (out_buf)
        free(out_buf);
    return ret;
}

int compress_file_tgz_isal(struct comp_compressor *compressor,
                           struct comp_input_centre_hdr *icc_hdr,
                           struct comp_output_centre *occ)
{
    if (unlikely(!icc_hdr || !icc_hdr->icc_list_hdr))
        return COMP_FAIL;

    if (icc_hdr->icc_list_hdr->buf) {
        /* 基于内存压缩 */
        //printf("tgz compress file %s, isal by mem\n", icc_hdr->icc_list_hdr->ipi.path);
        return compress_file_tgz_isal_mem(compressor, icc_hdr, occ);
    } else {
        /* 基于文件流压缩 */
        if (COMPRESS_INPUT_DIR == icc_hdr->type) {
            //printf("tgz compress dir %s, isal by stream\n",
            //icc_hdr->icc_list_hdr->ipi.path);
            return compress_file_tgz_isal_stream_dir(compressor, icc_hdr, occ);
        } else {
            //printf("tgz compress file (%s..), isal by stream\n",
            //icc_hdr->icc_list_hdr->ipi.path);
            return compress_file_tgz_isal_stream_file(compressor, icc_hdr, occ);
        }
    }
}
