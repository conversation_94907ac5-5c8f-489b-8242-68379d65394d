#ifndef __COMPRESS_H__
#define __COMPRESS_H__

#include <stdint.h>
#include <sys/stat.h>
#include <sys/types.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* 返回码 */
#define COMP_OK                     0
#define COMP_FAIL                   -1
#define COMP_PATH_OVER              -2      //path超长
#define COMP_ERR_MKDIR              -3      //文件夹创建失败
#define COMP_ERR_ALLOC              -4      //内存申请失败
#define COMP_NO_SUPPORT_PSWD        -5      //不支持加密

/**
 * 压缩器类型：isal/deflate.
 */
enum compressor_type {
#define COMPRESSOR_TYPE_DEFAULT      COMPRESSOR_TYPE_ISAL
    COMPRESSOR_TYPE_ISAL = 0,
    COMPRESSOR_TYPE_DEFLATE,
    COMPRESSOR_TYPE_MAX
};

/**
 * 压缩级别 fastest, medium/default, slow, slowest
 */
enum compress_level {
    COMPRESS_LEVEL_FASTEST = 0,
    COMPRESS_LEVEL_MEDIUM,
    COMPRESS_LEVEL_SLOW,
    COMPRESS_LEVEL_SLOWEST,
    COMPRESS_LEVEL_NONE,
#define COMPRESS_LEVEL_DEFAULT   COMPRESS_LEVEL_MEDIUM
};

/** 压缩格式类型 */
enum compress_mode {
    COMPRESS_MODE_NONE = 0,   /* 不压缩 */
    COMPRESS_MODE_ZIP,        /* zip */
    COMPRESS_MODE_GZIP,       /* gzip */
    COMPRESS_MODE_TGZ,        /* tar.gz */
    COMPRESS_MODE_MAX
};

/** 压缩处理输入类型 */
enum compress_input {
    COMPRESS_INPUT_FILE = 0,    /* 普通文件类型 */
    COMPRESS_INPUT_LIST,        /* 文件列表 */
    COMPRESS_INPUT_DIR,         /* 文件夹 */
    COMPRESS_INPUT_MAX
};

#define COMP_MAX_LEN_256    256
#define COMP_MAX_LEN_512    512

/**
 * 输入文件路径解析信息.
 */
struct comp_in_path_info {
#define COMP_IN_FILE_NAME_LEN(ipi)       \
    ((ipi)->file_len + (ipi)->suffix_len)
#define COMP_IN_FILE_PATH_ALL_LEN(ipi)   \
    ((ipi)->file_len + (ipi)->suffix_len + (ipi)->path_len)
#define COMP_IN_DIR_PATH_ALL_LEN(ipi)   \
    ((ipi)->dir_len + (ipi)->path_len)
#define COMP_IN_FILE_PATH_FILE_LEN(ipi)  \
    ((ipi)->file_len + (ipi)->path_len)
#define COMP_IN_FILE_NAME(ipi)           \
    ((ipi)->path + (ipi)->path_len)
    /* eg. /root/1234.txt */
    uint16_t suffix_len;        /**< 后缀长度(含.) (.txt) */
    uint16_t dir_len;           /**< 目录长度 (/data/haohan 中的haohan/) */
    uint16_t file_len;          /**< 文件名长度(不含文件后缀) (1234) */
    uint16_t path_len;          /**< 文件绝对路径长度(不含文件名) (/root/; /data/) */
    char path[COMP_MAX_LEN_256];  /**< 文件或目录全路径 (/root/1234.txt; /data/haohan/) */
};

/**
 * 输入文件核心信息.
 */
struct comp_input_centre {
    char *inter_name;               /**< 解压后文件名:file/list有效 */
    char *buf;                      /**< 映射内存部分 */
    struct comp_in_path_info ipi;   /**< 输入文件路径信息 */
    struct stat filestat;           /**< 输入文件stat信息 */
    struct comp_input_centre *next; /**< 下一项 */
};

/**
 * 输入文件信息头
 */
struct comp_input_centre_hdr {
    enum compress_input type;                   /**< 输入类型 */
    struct comp_input_centre *icc_list_hdr;     /**< 输入文件链 */
};

/**
 * 输出文件核心信息
 */
struct comp_output_centre {
    enum compress_mode mode;    /**< 压缩格式 */
    uint32_t map_sz;            /**< 映射长度 */
    uint32_t buf_sz;            /**< 实际压缩长度 */

    /* start: 内存由内部管理 */
    void *buf;                  /**< 压缩内容 */
    char *comp_name;            /**< 压缩文件名:可自定义，否则内部选择 */
    /* end: 内存由内部管理 */

    char *comp_path;            /**< 压缩文件本地存储路径 */
    char *comp_pswd;            /**< 压缩包密码:外部指定 */
};

/**
 * 创建压缩文件名.
 *
 * @param icc
 *  压缩输入文件核心信息
 * @param occ
 *  压缩输出文件核心信息
 * @param file_name
 *  存储待创建文件名.
 * @param file_name_len
 *  name可用长度.
 * @param concern_local
 *  是否关心本地目录，0时仅生成基于icc/occ的压缩文件名，1时生成包含本地目录的全路径.
 * @return
 *  COMP_OK: 创建成功.
 *  COMP_FAIL: 创建失败.
 */
int compress_out_file_name_init(struct comp_input_centre *icc,
                                struct comp_output_centre *occ,
                                char *file_name,
                                size_t file_name_len,
                                uint8_t concern_local);

/**
 * 添加压缩输入文件核心节点
 *
 * @param icc_hdr
 *  压缩输入文件信息结构链头
 * @param icc
 *  压缩输入文件节点
 * @return
 *  COMP_OK: 添加成功.
 *  COMP_FAIL: 添加失败.
 */
int comp_input_centre_add(struct comp_input_centre_hdr *icc_hdr,
                          struct comp_input_centre *icc);

/**
 * 依据类型,解析输入文件全路径信息
 *
 * @param ipi
 *  解析信息转储结构.
 * @param path
 *  绝对路径.
 * @param dir_base_len
 *  指定的文件夹基始长度.
 * @param type
 *  类型.
 * @return
 *  COMP_OK: 解析成功.
 *  COMP_FAIL: 解析失败.
 */
int comp_input_path_parse(struct comp_in_path_info *ipi,
                          char *path,
                          uint32_t dir_base_len,
                          enum compress_input type);

/**
 * 创建压缩器
 *
 * @param type
 *  指定压缩器类型.
 * @param level
 *  指定压缩级别.
 * @return
 *  NULL: 创建失败.
 *  非NULL: 创建成功，返回压缩器.
 */
void *compressor_creat(enum compressor_type type, enum compress_level level);

/**
 * 压缩输入文件信息头初始化
 *
 * @param icc_hdr
 *  压缩输入文件信息结构链头
 * @param input_type
 *  输入类型.
 */
void compress_icc_hdr_init(struct comp_input_centre_hdr *icc_hdr, uint8_t input_type);

/**
 * 压缩输入文件信息核心节点初始化
 *
 * @param icc
 *  压缩输入文件信息核心节点
 */
void compress_icc_init(struct comp_input_centre *icc);

/**
 * 压缩输入文件信息核心节点反初始化
 *
 * @param icc
 *  压缩输入文件信息核心节点
 */
void compress_icc_deinit(struct comp_input_centre *icc);

/**
 * 压缩输出文件信息核心节点初始化
 *
 * @param occ
 *  压缩输出文件信息核心节点
 * @param comp_path
 *  输出文件生成的绝对路径目录
 * @param comp_pswd
 *  输出压缩包密码
 * @param mode
 *  压缩格式
 */
void compress_occ_init(struct comp_output_centre *occ,
                       char *comp_path,
                       char *comp_pswd,
                       uint8_t mode);
/**
 * 压缩输出文件信息核心节点压缩路径初始化
 *
 * @param occ
 *  压缩输出文件信息核心节点
 * @param comp_path
 *  输出文件生成的绝对路径目录
 */
void compress_occ_path_init(struct comp_output_centre *occ,
                            char *comp_path);
/**
 * 压缩输出文件信息核心节点压缩密码初始化
 *
 * @param occ
 *  压缩输出文件信息核心节点
 * @param comp_pswd
 *  输出压缩包密码
 */
void compress_occ_pswd_init(struct comp_output_centre *occ,
                            char *comp_pswd);

/**
 * 压缩输出文件信息核心节点反初始化
 *
 * @param occ
 *  压缩输出文件信息核心节点
 */
void compress_occ_deinit(struct comp_output_centre *occ);

/**
 * 基于内存的数据压缩接口
 *
 * @param compressor
 *  压缩器
 * @param in
 *  待压缩数据起始地址
 * @param src_len
 *  待压缩数据长度
 * @param out
 *  压缩数据输出内存起始地址
 * @param dst_len
 *  待输出内存可用长度
 * @return
 *  实际压缩后数据长度.
 */
size_t compress_dispose_data(void *compressor,
                             const char *in,
                             uint32_t src_len,
                             char *out,
                             uint32_t dst_len);

/**
 * 文件压缩接口
 *
 * @param compressor
 *  压缩器
 * @param icc_hdr
 *  输入文件信息链头
 * @param occ
 *  压缩输出文件核心信息
 * @return
 *  COMP_OK: 压缩成功.
 *  COMP_FAIL: 压缩失败.
 */
int compress_dispose_file(void *compressor,
                          struct comp_input_centre_hdr *icc_hdr,
                          struct comp_output_centre *occ);

/**
 * 创建自定义压缩文件名内存
 *
 * @param occ
 *  压缩输出文件核心信息
 * @param len
 *  自定义压缩文件名预期长度
 * @return
 *  NULL: 创建失败
 *  非NULL: 创建成功返回实际内存.
 */
void *compress_outfile_name_creat(struct comp_output_centre *occ, uint32_t len);

/**
 * 创建自定义解压内文件名内存
 *
 * @param icc
 *  压缩输入文件核心信息
 * @param len
 *  自定义解压内文件名预期长度
 * @return
 *  NULL: 创建失败
 *  非NULL: 创建成功返回实际内存.
 */
void *compress_infile_name_creat(struct comp_input_centre *icc, uint32_t len);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __COMPRESS_H__ */
