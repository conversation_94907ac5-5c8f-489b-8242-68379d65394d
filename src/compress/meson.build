comp_includes = []
comp_sources = []
comp_libraries = []

comp_includes += global_includes
comp_includes += include_directories('.','include')
comp_includes += include_directories('../common/include')

comp_deps = [isal,deflate]
#comp_largs = ['-lcrypto']

subdir('include')

comp_sources += files(
    'compress.c',
    'comp_gz.c',
    'comp_zip.c',
    'comp_tgz.c',
)

comp_lib = shared_library(
    'hcompress',
    comp_sources,
    c_args: global_cflags,
    include_directories: comp_includes,
    dependencies: comp_deps,
    # link_with: ,
    # link_args: comp_largs,
    install: true,
)

comp_libraries += comp_lib

hcompress = declare_dependency(include_directories: 'include', link_with: comp_lib)
