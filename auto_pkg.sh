#!/bin/sh

BUILD_TYPE=$1

# 检查参数是否为空
if [ -z "$BUILD_TYPE" ]; then
    echo "Usage: $0 [debug|release]"
    exit 1
fi

# 处理不同的模式
case "$BUILD_TYPE" in
    debug)
        echo "Start building debug version ..."
        ;;
    release)
        echo "Start building release version ..."
        ;;
    *)
        echo "Error: Invalid mode '$BUILD_TYPE'. Must be 'debug' or 'release'."
        exit 1
        ;;
esac

PREFIX=/usr/local
OS_RELEASE_FILE="/etc/os-release"
VER_FILE="./VERSION"
ARCH=$(uname -i)
OS_NAME=$(grep "^ID=" "$OS_RELEASE_FILE" | cut -d '"' -f 2)
VER=$(cat "$VER_FILE")

#PKG_DIR=hhfiletrans-$VER-$BUILD_TYPE-$OS_NAME-$ARCH
PKG_DIR=hhfiletrans-$VER-$ARCH
EG_DIR=$PKG_DIR/examples
HDR_DIR=$PKG_DIR/include
LIB_DIR=$PKG_DIR/lib64
PKGCFG_DIR=$LIB_DIR/pkgconfig

rm build -rf
rm $PKG_DIR -rf
rm $PKG_DIR.tar.gz -rf

if [ "$ARCH" == "x86_64" ]; then
    #mkdir build && meson setup -Dbuildtype=$BUILD_TYPE -Dexamples=true -Dmachine=core-avx-i build
    mkdir build && meson setup -Dbuildtype=debug -Dexamples=true -Dmachine=core-avx-i build
else
    #mkdir build && meson setup -Dbuildtype=$BUILD_TYPE -Dexamples=true build
    mkdir build && meson setup -Dbuildtype=debug -Dexamples=true build
fi

# ninja -C build
meson compile -C build
meson install -C build >/dev/null

mkdir -p $PKG_DIR
mkdir -p $EG_DIR
mkdir -p $HDR_DIR
mkdir -p $LIB_DIR
mkdir -p $PKGCFG_DIR

cp -a tools/install.sh $PKG_DIR
cp -a build/examples/test-filetrans/master $EG_DIR
cp -a build/examples/test-filetrans/slave $EG_DIR
cp -a $PREFIX/lib64/libhcompress.so* $LIB_DIR
cp -a $PREFIX/lib64/libhfiletrans.so* $LIB_DIR
cp -a $PREFIX/lib64/pkgconfig/libhfiletrans.pc $PKGCFG_DIR
cp -a $PREFIX/include/filetrans $HDR_DIR

#find build/src/filetrans/ -maxdepth 1 \( -type l -o -type f \) -name "libhfiletrans*" -exec mv {} $LIB_DIR \;
#mv build/examples/test-filetrans/test-ft $EG_DIR
#mv build/src/compress/libhcompress.so $LIB_DIR
#mv build/meson-private/libhfiletrans.pc $PKGCFG_DIR
#mv build/ft_build_config.h $HDR_DIR
#cp src/filetrans/include/filetrans.h $HDR_DIR
#cp src/compress/include/compress.h $HDR_DIR

tar -czvf $PKG_DIR.tar.gz $PKG_DIR
rm $PKG_DIR -rf
echo "creat $PKG_DIR.tar.gz success!"

echo "creat filetrans rpm..."

#修改spec文件中版本号
sed -i "s/^Version:.*/Version:        $VER/" hfiletrans.spec
sed -i "s/^Source0:.*/Source0:        $PKG_DIR.tar.gz/" hfiletrans.spec

sed -i '/^%prep$/{:a; n; /^%build$/!{d; ba}}' hfiletrans.spec
sed -i "/^%prep/a\\
%setup -q -n $PKG_DIR
" hfiletrans.spec

#创建rpm打包环境，执行失败需安装：
#yum install -y rpm-build
#yum install -y rpmdevtools
if [ -d /root/rpmbuild ]; then
    rm -rf /root/rpmbuild
fi
rpmdev-setuptree

cp $PKG_DIR.tar.gz /root/rpmbuild/SOURCES/
cp hfiletrans.spec /root/rpmbuild/SPECS/
rpmbuild -ba /root/rpmbuild/SPECS/hfiletrans.spec
cp /root/rpmbuild/RPMS/x86_64/hh-filetrans*.rpm ./

echo "creat hhfiletrans rpm success!"
