ts_sources = files(
    'main.c',
    'test_output.c',
    'test_tsf1.c',
    'test_tsf2.c',
    'test_tsf3.c',
    'test_tsf4.c',
    'test_tsf5.c',
)

dpdk = dependency('libdpdk')
ts_deps = [dpdk,thread]
ts_libs = [ft_lib,comp_lib]

executable(
    'master',
    ts_sources,
    include_directories: global_includes,
    dependencies: ts_deps,
    link_with: ts_libs,
    link_args: '-ldl',
)

executable(
    'slave',
    'slave.c',
    include_directories: global_includes,
    dependencies: ts_deps,
    link_with: ts_libs,
    link_args: '-ldl',
)
