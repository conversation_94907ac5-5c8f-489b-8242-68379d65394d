// tgz + sftp + copy 例, 包含压缩包名自定义，同步备份磁盘功能

#include "test.h"

static struct ft_transfer ft_tsf2;

int test_ft_tsf2_reg(void)
{
    if (FT_OK != ft_transfer_reg(&ft_tsf2))
        return -1;
    return 0;
}

int test_tsf2_init(struct ft_transfer *tsf)
{
    struct ft_tsf_cfg *cfg = &tsf->cfg;
    memset(cfg, 0, sizeof(*cfg));

    cfg->compress_type = FT_COMPRESS_TGZ;
    strcpy(cfg->comp_path, "/home/<USER>/data/back/test");

    cfg->plat_idx[0] = 0;
    cfg->plat_cnt = 1;

    struct ft_up_platform *plat = &cfg->platform[0];
    strcpy(plat->name, "tsf2_plat");
    plat->up_srv_mode = FT_UP_SRV_COPY;
    plat->srv_cnt = 1;

    struct ft_up_srv *srv = &plat->upsrv[0];
    srv->ipver = FT_IP_VER4;
    srv->up_mode = FT_UPLOAD_SFTP;
    srv->port = 22;
    strcpy(srv->user, "root");
    strcpy(srv->pswd, "tma1100");
    strcpy(srv->host, "**********");
    strcpy(srv->path, "/root/filetrans");

    return FT_OK;
}

int test_tsf2_comp_pre(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
#if 0
    /* 压缩前定义压缩名 */
#define TEST_NEW_COMP_NAME  "this_is_new_comp_name"
    if (!info->is_comp || info->icc_hdr.type != COMPRESS_INPUT_DIR)
        return FT_OK;

    size_t len = strlen(TEST_NEW_COMP_NAME);
    char *new_name = compress_outfile_name_creat(&info->occ, len + 1);
    memcpy(new_name, TEST_NEW_COMP_NAME, len);
    new_name[len] = 0;
#endif
    return FT_OK;
}

int test_tsf2_comp_back(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    if (!info->is_comp)
        return FT_OK;

    /* 压缩后定义压缩名 */
    char old_file_name[FT_MAX_LEN_256];
    char new_file_name[FT_MAX_LEN_256];

    size_t len;
    ft_outfile_local_name_creat(info, old_file_name, sizeof(old_file_name));
    len = strlen(old_file_name);
    memcpy(new_file_name, old_file_name, len);
    new_file_name[len] = 0;

    len = 10;
    char *new_name = compress_outfile_name_creat(&info->occ, len);
    snprintf(new_name, len, "%s%u", "apt", info->occ.buf_sz);

    char *p = strrchr(new_file_name, '/');
    sprintf(p+1, "%s.tar.gz", new_name);
    rename(old_file_name, new_file_name);
    printf("rename %s to %s\n", old_file_name, new_file_name);

#if 0
    /* 基于内存压缩文件，msync同步到磁盘 */
    if (COMP_OK != compress_outfile_backup_local(&info->occ))
        return FT_FAIL;
#endif

    return FT_OK;
}

static int test_tsf2_upload_platform(struct ft_transfer *tsf,
                                     struct ft_file_info *info,
                                     struct ft_up_srv *srv,
                                     void *attr,
                                     void *usr_priv)
{
    int ret;
    char varDir[256];
    char url[FT_MAX_LEN_1024];
    snprintf(varDir, sizeof(varDir), "%s", tsf->name);

    /* 创建上报url */
    size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url,
            srv->path, varDir);
    ret = ft_outfile_up_name_creat(info, url + len, sizeof(url) - len);
    if (ret != FT_OK) {
        printf("ft_outfile_up_name_creat err(url = %s)", url);
        return FT_FAIL;
    }
    /* 上报tsf info文件(tsf file上报失败计数内部提供) */
    ret = ft_up_tsf_file(attr, srv, info, url);
    if (ret != FT_OK)
        return FT_FAIL;

    return FT_OK;
}

int test_tsf2_upload(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    return ft_tsf_platform_upload(tsf, info, attr, NULL, test_tsf2_upload_platform);
#if 0
    int i;
    struct ft_up_srv *srv;
    for (i = 0; i < cfg->srv_cnt; i++) {
        srv = &cfg->upsrv[i];
        /* 创建上报url */
        size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url,
                srv->path, varDir);
        ret = ft_comp_outfile_name_creat(info, url + len, sizeof(url) - len, 0);
        if (ret != FT_OK) {
            printf("ft_comp_outfile_name_creat err(url = %s)", url);
            return FT_FAIL;
        }
        /* 上报tsf info文件(tsf file上报失败计数内部提供) */
        ret = ft_up_tsf_file(truck, srv, info, url);
        if (ret != FT_OK)
            return FT_FAIL;
    }

    return FT_OK;
#endif
}

static struct ft_transfer ft_tsf2 = {
    .type = FT_TEST_TSF_TYPE2,
    .name = FT_TEST_TSF_TYPE2_NAME,
    .init = test_tsf2_init,
    .hook = {
        [FT_FILE_COMP_PRE] = test_tsf2_comp_pre,
        [FT_FILE_COMP_BACK] = test_tsf2_comp_back,
        [FT_FILE_UPLOAD] = test_tsf2_upload,
    },
};
