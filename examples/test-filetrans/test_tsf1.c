// zip + ftp + disp 例，包含压缩包内文件名自定义

#include "test.h"

static struct ft_transfer ft_tsf1;

int test_ft_tsf1_reg(void)
{
    if (FT_OK != ft_transfer_reg(&ft_tsf1))
        return -1;
    return 0;
}

int test_tsf1_init(struct ft_transfer *tsf)
{
    struct ft_tsf_cfg *cfg = &tsf->cfg;
    memset(cfg, 0, sizeof(*cfg));

    cfg->compress_type = FT_COMPRESS_ZIP;
    strcpy(cfg->comp_pswd, "tjf");
    strcpy(cfg->comp_path, "/home/<USER>/data/back/test");
    cfg->plat_idx[0] = 0;
    cfg->plat_cnt = 1;

    struct ft_up_platform *plat = &cfg->platform[0];

    strcpy(plat->name, "tsf1_plat");
    plat->up_srv_mode = FT_UP_SRV_DISP;
    plat->srv_cnt = 1;

    struct ft_up_srv *srv = &plat->upsrv[0];
    srv->ipver = FT_IP_VER4;
    srv->up_mode = FT_UPLOAD_FTP;
    srv->port = 21;
    strcpy(srv->user, "root");
    strcpy(srv->pswd, "tma1100");
    strcpy(srv->host, "**********");
    strcpy(srv->path, "/root/filetrans");

    return FT_OK;
}

static int test_tsf1_upload_platform(struct ft_transfer *tsf,
                                     struct ft_file_info *info,
                                     struct ft_up_srv *srv,
                                     void *attr,
                                     void *usr_priv)
{
    int ret;
    char url[FT_MAX_LEN_1024];
    char varDir[256];
    struct ft_up_truck *truck = attr;

    snprintf(varDir, sizeof(varDir), "%s", tsf->name);
    uint32_t up_priv = *(uint32_t*)ft_upload_priv_get(truck->upload, TEST_UP_PRIV_IDX);
    printf("up_priv = %u\n", up_priv);

    /* 创建上报url */
    size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url, srv->path, varDir);
    ret = ft_outfile_up_name_creat(info, url + len, sizeof(url) - len);
    if (ret != FT_OK) {
        printf("ft_outfile_up_name_creat err(url = %s)", url);
        return FT_FAIL;
    }

    /* 上报tsf info文件(tsf file上报失败计数内部提供) */
    ret = ft_up_tsf_file(truck, srv, info, url);
    if (ret != FT_OK)
        return FT_FAIL;

    return FT_OK;
}

int test_tsf1_upload(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    return ft_tsf_platform_upload(tsf, info, attr, NULL, test_tsf1_upload_platform);
}

int test_tsf1_acq_back(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    struct ft_acq_truck *truck = attr;
    uint32_t acq_priv = *(uint32_t*)ft_acq_priv_get(truck->acq, TEST_ACQ_PRIV_IDX);
    printf("acq_priv = %u\n", acq_priv);
    return FT_OK;
}

int test_tsf1_comp_pre(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
#define TEST_NEW_INTER_NAME  "this_is_new_inter_name"
    if (!info->is_comp || info->icc_hdr.type != COMPRESS_INPUT_LIST)
        return FT_OK;

    uint32_t idx = 0;
    char *new;
    char inter_name[256];

    struct comp_input_centre *icc = info->icc_hdr.icc_list_hdr;
    while (icc) {
        snprintf(inter_name, sizeof(inter_name), "%s_%d", TEST_NEW_INTER_NAME, idx++);
        new = compress_infile_name_creat(icc, strlen(inter_name));
        strcpy(new, inter_name);
        icc = icc->next;
    }

#if 0
    size_t len = strlen(TEST_NEW_INTER_NAME);
    char *new_name = compress_infile_name_creat(info->icc_hdr.icc_list_hdr, len);
    memcpy(new_name, TEST_NEW_INTER_NAME, len);
    new_name[len] = 0;
#endif

    return FT_OK;
}

static struct ft_transfer ft_tsf1 = {
    .type = FT_TEST_TSF_TYPE1,
    .name = FT_TEST_TSF_TYPE1_NAME,
    .init = test_tsf1_init,
    .hook = {
        [FT_FILE_ACQ_BACK] = test_tsf1_acq_back,
        [FT_FILE_COMP_PRE] = test_tsf1_comp_pre,
        [FT_FILE_UPLOAD] = test_tsf1_upload,
    },
};
