#include <getopt.h>
#include <stdarg.h>
#include <signal.h>
#include <rte_eal.h>
#include "test.h"

#define no_argument       0
#define required_argument 1
#define optional_argument 2

struct test_acq_param {
    int param1;
};

struct test_up_param {
    int param1;
};

struct ft_cfg g_cfg;
struct option long_opts[] = {
    {"help", no_argument, NULL, 'h'},
    {"compress-thread", required_argument, NULL, 'c'},
    {"upload-thread", required_argument, NULL, 'u'},
    {"acquire-thread", required_argument, NULL, 'a'},
    {NULL, 0, NULL, 0},
};

static void usage(void) {
    printf("usage:\n"
            "  -a list:<lcores> count:<num>        list:6 or count:1\n"
            "  -u list:<lcores> count:<num>        list:4,5 or count:2\n"
            "  -c list:<lcores> count:<num>        list:1-3 or count:3\n"
            "  -h, -?                              help\n");
}

static int parse_lcores(char *lcore_str, uint16_t *lcores, uint8_t lcore_cnt)
{
    int i;
    int ret, idx = 0, min, max, tmp;
    char *current = lcore_str;

    if (strncmp(current, "list:", 5) == 0) {
        current += 5;
    } else if (strncmp(current, "count:", 6) == 0) {
        current += 6;
        idx = atoi(current);
        if (idx > lcore_cnt) {
            return -1;
        }
        for (i = 0; i < idx; i++) {
            lcores[i] = 0;
        }
        return idx;
    } else {
        return -1;
    }

    do {
        current = strtok(current, ",");
        if (NULL == current)
            break;
        if (strchr(current, '-') != NULL) {
            ret = sscanf(current, "%d-%d", &min, &max);
            if (ret != 2 || min >= max)
                printf("arr error %s\n", current);
            for (ret = min; ret <= max; ret++) {
                lcores[idx++] = ret;
                if (idx >= lcore_cnt) {
                    printf("arr error %s: index=%d len=%d\n", lcore_str, idx, lcore_cnt);
                    goto end;
                }
            }
        } else {
            ret = sscanf(current, "%d", &tmp);
            if (1 != ret)
                printf("arr error %s\n", current);
            lcores[idx] = tmp;
            idx++;
            if (idx >= lcore_cnt) {
                printf("arr error %s: index=%d len=%d\n", lcore_str, idx, lcore_cnt);
                goto end;
            }
        }
        current = NULL;
    } while (1);

end:
    return idx;
}


static void parse_args(int argc, char **argv)
{
    int opt;
    int opt_idx;

    if (argc < 3) {
        usage();
        exit(1);
    }

#define SHORTOPTS "c:u:a:h?"

    opt = getopt_long(argc, argv, SHORTOPTS, long_opts, &opt_idx);
    while (opt != -1) {
        switch (opt) {
            case '?':
            case 'h':
                usage();
                exit(0);
            case 'c':
                g_cfg.comp_cnt = parse_lcores(optarg, g_cfg.comp_core,
                        sizeof(g_cfg.comp_core) / sizeof(g_cfg.comp_core[0]));
                if (g_cfg.comp_cnt <= 0) {
                    printf("invalid compress-thread '%s'\n", optarg);
                    exit(1);
                }
                break;
            case 'u':
                g_cfg.up_cnt = parse_lcores(optarg, g_cfg.upload_core,
                        sizeof(g_cfg.upload_core) / sizeof(g_cfg.upload_core[0]));
                if (g_cfg.up_cnt <= 0) {
                    printf("invalid upload-thread '%s'\n", optarg);
                    exit(1);
                }
                break;
            case 'a':
                if (0 >= parse_lcores(optarg, &g_cfg.acquire_core, 1)) {
                    printf("invalid acquire-thread '%s'\n", optarg);
                    exit(1);
                }
                break;
            default:
                break;
        }
        opt = getopt_long(argc, argv, SHORTOPTS, long_opts, &opt_idx);
    }
}

void ts_ft_log(uint8_t level, const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    printf("[Level: %d] ", level);
    vprintf(fmt, args);
    va_end(args);
}

static void *test_acq_priv_init(void *param, int bind_core)
{
    struct test_acq_param *a_param = param;
    if (!a_param)
        return NULL;
    uint32_t *acq_priv = malloc(sizeof(uint32_t));
    if (acq_priv) {
        *acq_priv = a_param->param1;
    }

    return acq_priv;
}

static void *test_up_priv_init(void *param, int bind_core)
{
    struct test_up_param *u_param = param;
    if (!u_param)
        return NULL;
    uint32_t *up_priv = malloc(sizeof(uint32_t));
    if (up_priv) {
        *up_priv = u_param->param1;
    }

    return up_priv;
}

static void __ft_ops_cfg_init(struct ft_ops_cfg *cfg, char *major_path)
{
    int len;
    /*
     * 默认运维原则：
     *  1. 上报异常及正常的文件均进行备份操作
     *  2. 不备份压缩前的原始文件
     *  3. 上报异常文件，异常时间超过3天，进行备份/删除
     *  4. 上报正常文件，即开始备份/删除
     *  5. 每日凌晨00:05开始删除已备份超过10天的日志文件
     *  6. 默认备份路径基于major_path
     * */
    //cfg->nor_valid = cfg->abn_valid = FT_OPS_TYPE_BACKUP;
    cfg->nor_valid = cfg->abn_valid = FT_OPS_TYPE_NONE;
    cfg->is_back_ori = 0;
    cfg->del_back_day = 10;
    cfg->abn_back_sec = (3 * 24 * 3600);
    strcpy(cfg->day_time, "14:42");
    len = snprintf(cfg->nor_back_path, sizeof(cfg->nor_back_path), "%s/backup/normal",
            major_path);
    if (len >= sizeof(cfg->nor_back_path)) {
        cfg->nor_back_path[0] = '\0';
    }
    len = snprintf(cfg->abn_back_path, sizeof(cfg->abn_back_path), "%s/backup/abnormal",
            major_path);
    if (len >= sizeof(cfg->nor_back_path)) {
        cfg->nor_back_path[0] = '\0';
    }
}

int main(int argc, char **argv)
{
    prctl(PR_SET_NAME, "filetrans-test");
    parse_args(argc, argv);

    int dargc = 3;
    char *dargv[] = {"master", "-l", "1,2"};
    int ret = rte_eal_init(dargc, dargv);
    if (ret < 0) {
        printf("rte_eal_init faild#%d.\n", ret);
        return -1;
    }

    signal(SIGINT, ft_signal_dispose);
    signal(SIGTERM, ft_signal_dispose);

    g_cfg.mproc = 1;            //开启多进程能力
    g_cfg.log_fn = ts_ft_log;
    strcpy(g_cfg.path, FT_PATH_ROOT);
    g_cfg.comp_level = COMPRESS_LEVEL_MEDIUM;

    __ft_ops_cfg_init(&g_cfg.ops, g_cfg.path);

    struct test_acq_param *acq_param = malloc(sizeof(struct test_acq_param));
    if (acq_param) {
        acq_param->param1 = 2024;
    }
    struct test_up_param *up_param = malloc(sizeof(struct test_up_param));
    if (up_param) {
        up_param->param1 = 2025;
    }

    test_ft_tsf1_reg();
    test_ft_tsf2_reg();
    test_ft_tsf3_reg();
    test_ft_tsf4_reg();
    test_ft_tsf5_reg();

    ft_start(&g_cfg);
    ft_acq_priv_set(test_acq_priv_init, acq_param, TEST_ACQ_PRIV_IDX);
    ft_upload_priv_set(test_up_priv_init, up_param, TEST_UP_PRIV_IDX);
    test_output_creat();

    while (1) {
        sleep(1);
    }

    return 0;
}
