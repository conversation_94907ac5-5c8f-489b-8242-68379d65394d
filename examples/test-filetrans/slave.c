#include "test.h"

int main(void)
{
    struct ft_mproc_msg *msg;

    /* 初始化 */
    msg = ft_mproc_slave_init();
    if (!msg) {
        printf("filetrans slave init failed!\n");
        return -1;
    }

    FILE *fp;
    char path[256];
    struct tsf3_info_priv *usr_priv;

    /* file */
    snprintf(path, sizeof(path), "%s/test_%s_slave.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE3_NAME);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);

        ft_mproc_slave_msg_start(msg);
        msg->log_type = FT_TEST_TSF_TYPE3;
        msg->sub_type = 0;
        msg->line = 1;
        msg->platform = 0;
        strcpy(msg->path, path);

        usr_priv = (void*)(&msg->usr_priv[0]);
        strcpy(usr_priv->cmd_id, "20250326");
        strcpy(usr_priv->house_id, "haohan-hefei-hangye");
        usr_priv->test = 2025;
        ft_mproc_slave_msg_stop(msg);
    }

    /* 反初始化 */
    ft_mproc_slave_deinit(msg);

    return 0;
}
