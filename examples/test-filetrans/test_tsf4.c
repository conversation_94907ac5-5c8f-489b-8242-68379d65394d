// none + sftp + disp + .ok 例

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "test.h"


static struct ft_transfer ft_tsf4;

int test_ft_tsf4_reg(void)
{
    if (FT_OK != ft_transfer_reg(&ft_tsf4))
        return -1;
    return 0;
}

int test_tsf4_init(struct ft_transfer *tsf)
{
    struct ft_tsf_cfg *cfg = &tsf->cfg;
    memset(cfg, 0, sizeof(*cfg));

    cfg->concern_sub = 1;
    strcpy(cfg->sub[TEST_TSF4_SUB_TYPE1].str, TEST_TSF4_SUB_TYPE1_STR);
    strcpy(cfg->sub[TEST_TSF4_SUB_TYPE2].str, TEST_TSF4_SUB_TYPE2_STR);

    cfg->compress_type = FT_COMPRESS_NONE;
    cfg->plat_idx[0] = 0;
    cfg->plat_cnt = 1;
    strcpy(cfg->comp_path, "/home/<USER>/data/back/test");

    struct ft_up_platform *plat = &cfg->platform[0];
    strcpy(plat->name, "tsf4_plat");
    plat->up_srv_mode = FT_UP_SRV_DISP;
    plat->srv_cnt = 1;

    struct ft_up_srv *srv = &plat->upsrv[0];
    srv->ipver = FT_IP_VER4;
    srv->up_mode = FT_UPLOAD_SFTP;
    srv->port = 22;
    strcpy(srv->user, "root");
    strcpy(srv->pswd, "tma1100");
    strcpy(srv->host, "**********");
    strcpy(srv->path, "/root/filetrans");

    return FT_OK;
}

static int test_complete_ok(struct ft_up_truck *truck,
                            struct ft_file_info *info,
                            const struct ft_up_srv *srv,
                            char *url)
{
#define TEST_SUF_OK     ".ok"
    char path[256];
    size_t pathFileLen = FT_FILE_PATH_FILE_LEN(&info->fc);
    size_t sufLen = strlen(TEST_SUF_OK);

    if (sizeof(path) <= (pathFileLen + sufLen))
        return FT_FAIL;

    /* /root/a/b.txt -> /root/a/b.ok */
    memcpy(path, info->fc.path, pathFileLen);
    memcpy(path + pathFileLen, TEST_SUF_OK, sufLen);
    path[pathFileLen + sufLen] = 0;

    int fd = open(path, O_CREAT|O_RDWR, 0777);
    if (fd < 0) {
        printf("open %s err", path);
        return FT_FAIL;
    }
    close(fd);

    char *p = strrchr(url, '.');
    memcpy(p, TEST_SUF_OK, sufLen);
    p[sufLen] = 0;

    return ft_up_new_file(truck, srv, path, url, 0);
}

static int test_tsf4_upload_platform(struct ft_transfer *tsf,
                                     struct ft_file_info *info,
                                     struct ft_up_srv *srv,
                                     void *attr,
                                     void *usr_priv)
{
    char varDir[256];
    snprintf(varDir, sizeof(varDir), "%s", tsf->name);

    int ret;
    char url[FT_MAX_LEN_1024];
    //struct ft_tsf_cfg *cfg = &tsf->cfg;
    struct ft_up_truck *truck = attr;

    /* 创建上报url */
    size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url, srv->path, varDir);
    ret = ft_outfile_up_name_creat(info, url + len, sizeof(url) - len);
    if (ret != FT_OK) {
        printf("ft_outfile_up_name_creat err(url = %s)", url);
        return FT_FAIL;
    }

    /* 上报tsf info文件(tsf file上报失败计数内部提供) */
    ret = ft_up_tsf_file(truck, srv, info, url);
    if (ret != FT_OK)
        return FT_FAIL;
    test_complete_ok(truck, info, srv, url);
    return FT_OK;
}

int test_tsf4_upload(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    return ft_tsf_platform_upload(tsf, info, attr, NULL, test_tsf4_upload_platform);
}

static struct ft_transfer ft_tsf4 = {
    .type = FT_TEST_TSF_TYPE4,
    .name = FT_TEST_TSF_TYPE4_NAME,
    .init = test_tsf4_init,
    .hook = {
        [FT_FILE_UPLOAD] = test_tsf4_upload,
    },
};

