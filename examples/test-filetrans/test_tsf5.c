// none + sftp + disp + .ok 例

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "test.h"

struct test_tsf5_priv {
    struct ft_up_srv *srv;
    struct ft_file_info csv;
};

static struct ft_transfer ft_tsf5;

int test_ft_tsf5_reg(void)
{
    if (FT_OK != ft_transfer_reg(&ft_tsf5))
        return -1;
    return 0;
}

int test_tsf5_init(struct ft_transfer *tsf)
{
    struct ft_tsf_cfg *cfg = &tsf->cfg;
    memset(cfg, 0, sizeof(*cfg));

    cfg->compress_type = FT_COMPRESS_NONE;
    cfg->plat_idx[0] = 0;
    cfg->plat_cnt = 1;

    struct ft_up_platform *plat = &cfg->platform[0];
    strcpy(plat->name, "tsf5_plat");
    plat->up_srv_mode = FT_UP_SRV_DISP;
    plat->srv_cnt = 1;

    struct ft_up_srv *srv = &plat->upsrv[0];
    srv->ipver = FT_IP_VER4;
    srv->up_mode = FT_UPLOAD_SFTP;
    srv->port = 22;
    strcpy(srv->user, "root");
    strcpy(srv->pswd, "tma1100");
    strcpy(srv->host, "**********");
    strcpy(srv->path, "/root/filetrans");

    return FT_OK;
}

static int test_tsf5_upload_platform(struct ft_transfer *tsf,
                                     struct ft_file_info *info,
                                     struct ft_up_srv *srv,
                                     void *attr,
                                     void *usr_priv)
{
    char varDir[256];
    snprintf(varDir, sizeof(varDir), "%s", tsf->name);

    int ret;
    char url[FT_MAX_LEN_1024];
    struct test_tsf5_priv *priv = usr_priv;
    struct ft_up_truck *truck = attr;

    printf("file_name = %s\n", FT_FILE_NAME(&info->fc));
    printf("dot = %s\n", strrchr(FT_FILE_NAME(&info->fc), '.'));

    char *dot = strrchr(FT_FILE_NAME(&info->fc), '.');
    if (dot && !strncmp(dot, ".csv", 4)) {
        memcpy(&priv->csv, info, sizeof(*info));
        priv->srv = srv;
        return FT_OK;
    }

    /* 创建上报url */
    size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url, srv->path, varDir);
    ret = ft_outfile_up_name_creat(info, url + len, sizeof(url) - len);
    if (ret != FT_OK) {
        printf("ft_outfile_up_name_creat err(url = %s)", url);
        return FT_FAIL;
    }

    /* 上报tsf info文件(tsf file上报失败计数内部提供) */
    ret = ft_up_tsf_file(truck, srv, info, url);
    if (ret != FT_OK)
        return FT_FAIL;
    return FT_OK;
}

int test_tsf5_upload(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    struct stat filestat;
    stat(info->fc.path, &filestat);
    printf("filestat.st_mtime = %lu\n", filestat.st_mtime);

    struct test_tsf5_priv priv;
    priv.srv = NULL;

    if (FT_OK != ft_tsf_platform_upload(tsf, info, attr, &priv,
                test_tsf5_upload_platform))
        return FT_FAIL;

    if (priv.srv) {
        char url[FT_MAX_LEN_1024];
        char varDir[256];
        struct ft_up_srv *srv = priv.srv;

        snprintf(varDir, sizeof(varDir), "%s", tsf->name);
        size_t len = snprintf(url, sizeof(url), "%s/%s/%s/",
                srv->seg_url, srv->path, varDir);
        int ret = ft_outfile_up_name_creat(&priv.csv, url + len, sizeof(url) - len);
        if (ret != FT_OK) {
            printf("ft_outfile_up_name_creat err(url = %s)", url);
            return FT_FAIL;
        }

        /* 上报tsf info文件(tsf file上报失败计数内部提供) */
        ret = ft_up_tsf_file(attr, srv, &priv.csv, url);
        if (ret != FT_OK)
            return FT_FAIL;
    }

    return FT_OK;
}

static struct ft_transfer ft_tsf5 = {
    .type = FT_TEST_TSF_TYPE5,
    .name = FT_TEST_TSF_TYPE5_NAME,
    .init = test_tsf5_init,
    .hook = {
        [FT_FILE_UPLOAD] = test_tsf5_upload,
    },
};

