// gz + ftp + disp 例

#include "test.h"

static struct ft_transfer ft_tsf3;

int test_ft_tsf3_reg(void)
{
    if (FT_OK != ft_transfer_reg(&ft_tsf3))
        return -1;
    return 0;
}

int test_tsf3_init(struct ft_transfer *tsf)
{
    struct ft_tsf_cfg *cfg = &tsf->cfg;
    memset(cfg, 0, sizeof(*cfg));

    cfg->compress_type = FT_COMPRESS_GZIP;
    //strcpy(cfg->comp_path, "/home/<USER>/data/back/test");
    cfg->plat_idx[0] = 0;
    cfg->plat_cnt = 1;

    struct ft_up_platform *plat = &cfg->platform[0];
    strcpy(plat->name, "tsf3_plat");
    plat->up_srv_mode = FT_UP_SRV_DISP;
    plat->srv_cnt = 1;

    struct ft_up_srv *srv = &plat->upsrv[0];
    srv->ipver = FT_IP_VER4;
    srv->up_mode = FT_UPLOAD_FTP;
    srv->port = 21;
    strcpy(srv->user, "root");
    strcpy(srv->pswd, "tma1100");
    strcpy(srv->host, "**********");
    strcpy(srv->path, "/root/filetrans");

    /* 创建tsf的fileinfo用户私有数据内存池 */
    if (FT_OK != ft_file_info_priv_pool_creat(tsf, sizeof(struct tsf3_info_priv), 0))
        return FT_FAIL;

    return FT_OK;
}

int test_tsf3_comp_pre(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    char comp_path[FT_MAX_LEN_256];
    struct tsf3_info_priv *usr_priv = info->attr.usr_priv;

    snprintf(comp_path, sizeof(comp_path), "%s/%s/%s/0x%x", FT_PATH_ROOT,
            usr_priv->cmd_id, usr_priv->house_id, usr_priv->test);

    compress_occ_path_init(&info->occ, comp_path);

    return FT_OK;
}

static int test_tsf3_upload_platform(struct ft_transfer *tsf,
                                     struct ft_file_info *info,
                                     struct ft_up_srv *srv,
                                     void *attr,
                                     void *usr_priv)
{
    char varDir[256];
    snprintf(varDir, sizeof(varDir), "%s", tsf->name);

    int ret;
    char url[FT_MAX_LEN_1024];
    //struct ft_tsf_cfg *cfg = &tsf->cfg;
    struct ft_up_truck *truck = attr;

    /* 创建上报url */
    size_t len = snprintf(url, sizeof(url), "%s/%s/%s/", srv->seg_url, srv->path, varDir);
    ret = ft_outfile_up_name_creat(info, url + len, sizeof(url) - len);
    if (ret != FT_OK) {
        printf("ft_outfile_up_name_creat err(url = %s)", url);
        return FT_FAIL;
    }

    /* 上报tsf info文件(tsf file上报失败计数内部提供) */
    ret = ft_up_tsf_file(truck, srv, info, url);
    if (ret != FT_OK)
        return FT_FAIL;

    return FT_OK;
}

int test_tsf3_upload(struct ft_transfer *tsf, struct ft_file_info *info, void *attr)
{
    return ft_tsf_platform_upload(tsf, info, attr, NULL, test_tsf3_upload_platform);
}

static uint32_t test_tsf3_ser(const void *usr_priv, char *ser_buf, uint32_t ser_buf_len)
{
    const struct tsf3_info_priv *priv = usr_priv;
    return snprintf(ser_buf, ser_buf_len, "%s,%s,%d", priv->cmd_id, priv->house_id,
            priv->test);
}

static int test_tsf3_deser(void *usr_priv, char *priv_str, uint32_t priv_str_len)
{
    char *p;
    struct tsf3_info_priv *priv = usr_priv;

    // cmd_id
    p = strtok(priv_str, ",");
    if (!p)
        return FT_FAIL;
    strcpy(priv->cmd_id, p);

    // house_id
    p = strtok(NULL, ",");
    if (!p)
        return FT_FAIL;
    strcpy(priv->house_id, p);

    // test
    p = strtok(NULL, ",");
    if (!p)
        return FT_FAIL;
    priv->test = atoi(p);

    return FT_OK;
}

static struct ft_transfer ft_tsf3 = {
    .type = FT_TEST_TSF_TYPE3,
    .name = FT_TEST_TSF_TYPE3_NAME,
    .init = test_tsf3_init,
    .ser = test_tsf3_ser,
    .deser = test_tsf3_deser,
    .hook = {
        [FT_FILE_COMP_PRE] = test_tsf3_comp_pre,
        [FT_FILE_UPLOAD] = test_tsf3_upload,
    },
};
