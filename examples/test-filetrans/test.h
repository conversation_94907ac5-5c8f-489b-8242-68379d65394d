#ifndef __TEST_H__
#define __TEST_H__

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdlib.h>
#include <sys/prctl.h>
#include <unistd.h>
#include "filetrans.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

enum {
#define FT_TEST_TSF_TYPE1_NAME  "ftTsf1"
    FT_TEST_TSF_TYPE1 = 0,
#define FT_TEST_TSF_TYPE2_NAME  "ftTsf2"
    FT_TEST_TSF_TYPE2,
#define FT_TEST_TSF_TYPE3_NAME  "ftTsf3"
    FT_TEST_TSF_TYPE3,
#define FT_TEST_TSF_TYPE4_NAME  "ftTsf4"
    FT_TEST_TSF_TYPE4,
#define FT_TEST_TSF_TYPE5_NAME  "ftTsf5"
    FT_TEST_TSF_TYPE5,
};

#define FT_TEST_OUTPUT_CNT      2
#define FT_PATH_ROOT            "/root/test/filetrans"

#define TEST_ACQ_PRIV_IDX   1 //私有数据索引
#define TEST_UP_PRIV_IDX    2 //私有数据索引

struct tsf3_info_priv {
    char cmd_id[64];
    char house_id[32];
    int test;
};

#define TEST_TSF4_SUB_TYPE1         0
#define TEST_TSF4_SUB_TYPE1_STR     "tsf4_sub1"
#define TEST_TSF4_SUB_TYPE2         1
#define TEST_TSF4_SUB_TYPE2_STR     "tsf4_sub2"

int test_ft_tsf1_reg(void);
int test_ft_tsf2_reg(void);
int test_ft_tsf3_reg(void);
int test_ft_tsf4_reg(void);
int test_ft_tsf5_reg(void);
int test_output_creat(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __TEST_H__ */
