#include <pthread.h>
#include "test.h"

extern struct ft_cfg g_cfg;
static void *g_out_ft_res[FT_TEST_OUTPUT_CNT];

void test_out_tsf1(uint8_t idx)
{
    FILE *fp;
    char path[256];
    void *file_list;
    struct ft_info_attr attr;
    memset(&attr, 0, sizeof(attr));

    /* file(comp) */
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE1_NAME, idx);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
        ft_info_put_acq(g_out_ft_res[idx], path, &attr, FT_TEST_TSF_TYPE1, FT_INFO_FILE);
    }

    /* dir */
    ft_info_put_acq(g_out_ft_res[idx], FT_PATH_ROOT, &attr, FT_TEST_TSF_TYPE1,
            FT_INFO_DIR);

    /* list */
    file_list = ft_file_list_creat(FT_TEST_TSF_TYPE1, g_out_ft_res[idx]);
    ft_file_list_add(file_list, path, g_out_ft_res[idx], 0);
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE1_NAME, idx + 1);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
    }
    ft_file_list_add(file_list, path, g_out_ft_res[idx], 0);
    ft_info_put_acq(NULL, file_list, &attr, FT_TEST_TSF_TYPE1,
            FT_INFO_LIST);
}

void test_out_tsf2(uint8_t idx)
{
    FILE *fp;
    char path[256];
    void *file_list;
    struct ft_info_attr attr;
    memset(&attr, 0, sizeof(attr));

    /* file */
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE2_NAME, idx);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
        attr.line = 1;
        attr.sub_type = 0;
        attr.usr_priv = NULL;
        ft_info_put_acq(g_out_ft_res[idx], path, &attr, FT_TEST_TSF_TYPE2, FT_INFO_FILE);
    }
    /* dir */
    ft_info_put_acq(g_out_ft_res[idx], FT_PATH_ROOT, &attr, FT_TEST_TSF_TYPE2,
            FT_INFO_DIR);
    /* list */
    file_list = ft_file_list_creat(FT_TEST_TSF_TYPE2, g_out_ft_res[idx]);
    ft_file_list_add(file_list, path, g_out_ft_res[idx], 0);
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE2_NAME, idx + 1);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
    }
    ft_file_list_add(file_list, path, g_out_ft_res[idx], 0);
    ft_info_put_acq(g_out_ft_res[idx], file_list, &attr, FT_TEST_TSF_TYPE2,
            FT_INFO_LIST);
}

void test_out_tsf3(uint8_t idx)
{
    FILE *fp;
    char path[256];
    struct ft_info_attr attr;
    struct tsf3_info_priv *usr_priv =
        ft_file_info_priv_mem_get(g_out_ft_res[idx], FT_TEST_TSF_TYPE3);

    memset(&attr, 0, sizeof(attr));
    if (usr_priv) {
        strcpy(usr_priv->cmd_id, "20250324");
        strcpy(usr_priv->house_id, "haohan-hefei");
        usr_priv->test = 0x900d;
    }

    /* file */
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE3_NAME, idx);
    fp = fopen(path, "w+");
    if (fp) {
        attr.line = 1;
        attr.sub_type = 0;
        attr.usr_priv = usr_priv;
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
        ft_info_put_acq(g_out_ft_res[idx], path, &attr, FT_TEST_TSF_TYPE3, FT_INFO_FILE);
    }
}

void test_out_tsf4(uint8_t idx)
{
    FILE *fp;
    char path[256];
    struct ft_info_attr attr;
    memset(&attr, 0, sizeof(attr));

    /* file */
    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE4_NAME, idx);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
        attr.sub_type = TEST_TSF4_SUB_TYPE1;
        ft_info_put_acq(g_out_ft_res[idx], path, &attr, FT_TEST_TSF_TYPE4, FT_INFO_FILE);
    }

    snprintf(path, sizeof(path), "%s/test_%s_comp_out%d_%d.txt",
            FT_PATH_ROOT, FT_TEST_TSF_TYPE4_NAME, idx, idx);
    fp = fopen(path, "w+");
    if (fp) {
        fwrite(path, strlen(path), 1, fp);
        fclose(fp);
        attr.sub_type = TEST_TSF4_SUB_TYPE2;
        ft_info_put_acq(g_out_ft_res[idx], path, &attr, FT_TEST_TSF_TYPE4, FT_INFO_FILE);
    }
}

void test_out_tsf5(uint8_t idx)
{
    FILE *fp;
    char dir[256];
    char path[512];
    struct ft_info_attr attr;
    memset(&attr, 0, sizeof(attr));

    /* mkdir */
    snprintf(dir, sizeof(dir), "%s/%s", FT_PATH_ROOT, FT_TEST_TSF_TYPE5_NAME);
    mkdir(dir, ACCESSPERMS);

    /* csv */
    snprintf(path, sizeof(path), "%s/index.csv", dir);
    fp = fopen(path, "w+");
    if (!fp)
        return;
    fwrite(path, strlen(path), 1, fp);
    fclose(fp);

    /* pic */
    snprintf(path, sizeof(path), "%s/pic", dir);
    fp = fopen(path, "w+");
    if (!fp)
        return;
    fwrite(path, strlen(path), 1, fp);
    fclose(fp);

    /* mp3 */
    snprintf(path, sizeof(path), "%s/mp3", dir);
    fp = fopen(path, "w+");
    if (!fp)
        return;
    fwrite(path, strlen(path), 1, fp);
    fclose(fp);

    ft_info_put_acq(g_out_ft_res[idx], dir, &attr, FT_TEST_TSF_TYPE5, FT_INFO_DIR);
}

static inline void __ftCmdPrintTsfStatHead(void)
{
}

static void test_upload_stat_show(void)
{
    int i;
    double Bps, fps;
    printf("%8s %16s %16s\n", "upload", "KBps", "fps");
    printf("----------------------------------\n");

    double total_Bps = 0, total_fps = 0;
    for (i = 0; i < g_cfg.up_cnt; i++) {
        ft_up_stat_get(&Bps, &fps, i);
        printf("%8d %16.2f %16.2f\n", i, Bps, fps);
        total_Bps += Bps;
        total_fps += fps;
    }

    printf("%8s %16.2f %16.2f\n", "total", total_Bps, total_fps);
}

static void *test_out_process(void* arg)
{
    int idx = *(int*)arg;
    char name[64];

    snprintf(name, sizeof(name), "ft_test_output%d", idx);
    prctl(PR_SET_NAME, name);

    g_out_ft_res[idx] = ft_acquire_res_creat(0, 0, name);
    if (!g_out_ft_res[idx]) {
        printf("ft acq res creat fail!\n");
        exit(-1);
    } else {
        printf("g_out_ft_res[%d] = %p\n", idx, g_out_ft_res[idx]);
    }

    while (1) {
        if (idx == 0) {
            test_out_tsf1(idx);
            test_out_tsf2(idx);
        } else {
            test_out_tsf3(idx);
            test_out_tsf4(idx);
            test_out_tsf5(idx);
        }

        sleep(2);
        if (idx == 0) {
            test_upload_stat_show();
        }

        while (1) {
            sleep(1);
        }
    }
}

int test_output_creat(void)
{
    int ret;
    pthread_t tid;

    int i;
    int *idx;
    for (i = 0; i < FT_TEST_OUTPUT_CNT; i++) {
        idx = malloc(sizeof(int));
        *idx = i;

        ret = pthread_create(&tid, NULL, test_out_process, idx);
        if (ret < 0) {
            return -1;
        } else {
            pthread_detach(tid);
        }
    }

    return 0;
}
