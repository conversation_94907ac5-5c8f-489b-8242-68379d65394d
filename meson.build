project(
    'hfiletrans',
    'c',
    version: run_command(find_program('cat'), files('VERSION')).stdout().strip(),
    default_options: [
        'buildtype=debug',
        'b_lundef=true',
        'includedir=include/filetrans',
    ],
)

host_os_name = host_machine.system()
host_cpu_name = host_machine.cpu()

# 平台兼容性检查(操作系统)
if host_os_name == 'linux'
    os_subdir = 'linux'
else
    error('unsupported system type @0@'.format(host_os_name))
endif

# 平台兼容性检查(CPU), aarch64暂不需更细粒度处理
if host_cpu_name.startswith('x86')
    arch_subdir = 'x86'
elif host_cpu_name.startswith('arm') or host_cpu_name.startswith('aarch')
    arch_subdir = 'arm'
else
    error('unsupported CPU type @0@'.format(host_cpu_name))
endif

# 变量声明
cc = meson.get_compiler('c')
ft_conf = configuration_data()
ft_headers = []
comp_headers = []
ft_libraries = []
ft_extra_ldflags = []
ft_includes = []
ft_sources = []
global_cflags = []
global_includes = []

# 工程选项
if get_option('buildtype').startswith('debug')
    add_project_arguments('-g3', '-gdwarf-2', language: 'c')
endif
add_project_arguments(
    '-Werror',
    '-Wfatal-errors',
    #'-Wno-unused-function',
    '-Wno-unused-result',
    language: 'c',
)

# 依赖项
thread = dependency('threads')
isal = dependency('libisal')
deflate = dependency('libdeflate')
curl = dependency('libcurl')

subdir('config')

global_includes += include_directories(
    '.',
)

# if not is_windows
global_cflags += machine_args
global_cflags += ext_args
# endif

build_cfg = 'ft_build_config.h'
configure_file(
    output: build_cfg,
    configuration: ft_conf,
    install: true,
    install_dir: get_option('includedir'),
)

subdir('src')

global_includes += ft_includes

if get_option('examples')
    subdir('examples')
endif

install_headers(ft_headers)
install_headers(comp_headers)
#install_headers(comp_headers, install_dir: 'include/compress')
subdir('tools/pkg_config')
